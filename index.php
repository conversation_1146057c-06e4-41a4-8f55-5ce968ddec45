<?php
// Error handling
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't show errors to users in production
ini_set('log_errors', 1);

// Redirect to installer if config is missing
if (!file_exists('config.php')) {
    header('Location: install.php');
    exit;
}

require_once 'config.php';

// Try to load optional includes with fallback
$security_loaded = false;
$optimization_loaded = false;

if (file_exists('includes/security.php')) {
    try {
        require_once 'includes/security.php';
        $security_loaded = true;
    } catch (Exception $e) {
        error_log("Security functions failed to load: " . $e->getMessage());
    }
}

if (file_exists('includes/database_optimization.php')) {
    try {
        require_once 'includes/database_optimization.php';
        $optimization_loaded = true;
    } catch (Exception $e) {
        error_log("Database optimization failed to load: " . $e->getMessage());
    }
}

// Database connection with better error handling
try {
    $dbPath = __DIR__ . '/' . DB_FILE;

    // Check if database file exists
    if (!file_exists($dbPath)) {
        throw new Exception('ملف قاعدة البيانات غير موجود. يرجى إعادة تثبيت النظام.');
    }

    $pdo = new PDO('sqlite:' . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

    // Validate database structure (optional)
    if ($security_loaded && function_exists('validateDatabaseConnection')) {
        try {
            if (!validateDatabaseConnection($dbPath)) {
                error_log('Database validation failed, but continuing...');
            }
        } catch (Exception $e) {
            error_log("Database validation error: " . $e->getMessage());
        }
    }

    // Initialize database optimization (optional)
    if ($optimization_loaded && function_exists('initDatabaseOptimization')) {
        try {
            initDatabaseOptimization($pdo);
        } catch (Exception $e) {
            error_log("Database optimization error: " . $e->getMessage());
        }
    }

} catch (PDOException $e) {
    error_log("Database connection error: " . $e->getMessage());
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>خطأ في النظام</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; text-align: center; }
            .error-container { max-width: 600px; margin: 100px auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
            .error { color: #721c24; background: #f8d7da; padding: 20px; border-radius: 8px; border: 1px solid #f5c6cb; margin: 20px 0; }
            .btn { display: inline-block; padding: 12px 24px; background: #8B4513; color: white; text-decoration: none; border-radius: 6px; margin: 10px; }
        </style>
    </head>
    <body>
        <div class="error-container">
            <h1>❌ خطأ في النظام</h1>
            <div class="error">
                <p>عذراً، حدث خطأ في الاتصال بقاعدة البيانات.</p>
                <p>يرجى المحاولة لاحقاً أو الاتصال بالمدير.</p>
            </div>
            <p>
                <a href="debug_db.php" class="btn">🔍 فحص قاعدة البيانات</a>
                <a href="fix_db.php" class="btn">🔧 إصلاح قاعدة البيانات</a>
                <a href="install.php" class="btn">🚀 إعادة التنصيب</a>
            </p>
        </div>
    </body>
    </html>
    <?php
    exit;
} catch (Exception $e) {
    error_log("System error: " . $e->getMessage());
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>خطأ في النظام</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; text-align: center; }
            .error-container { max-width: 600px; margin: 100px auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
            .error { color: #721c24; background: #f8d7da; padding: 20px; border-radius: 8px; border: 1px solid #f5c6cb; margin: 20px 0; }
            .btn { display: inline-block; padding: 12px 24px; background: #8B4513; color: white; text-decoration: none; border-radius: 6px; margin: 10px; }
        </style>
    </head>
    <body>
        <div class="error-container">
            <h1>❌ خطأ في النظام</h1>
            <div class="error">
                <p><?php echo htmlspecialchars($e->getMessage()); ?></p>
            </div>
            <p>
                <a href="debug_db.php" class="btn">🔍 فحص قاعدة البيانات</a>
                <a href="fix_db.php" class="btn">🔧 إصلاح قاعدة البيانات</a>
                <a href="install.php" class="btn">🚀 إعادة التنصيب</a>
            </p>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Fetch settings with error handling
try {
    $stmt = $pdo->query("SELECT * FROM settings");
    $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

    // Validate required settings
    $requiredSettings = ['site_title', 'primary_color', 'secondary_color', 'lang_active'];
    foreach ($requiredSettings as $setting) {
        if (!isset($settings[$setting])) {
            throw new Exception("إعداد مطلوب مفقود: {$setting}");
        }
    }
} catch (Exception $e) {
    error_log("Settings error: " . $e->getMessage());
    die("خطأ في تحميل إعدادات النظام.");
}

// Fetch categories with error handling (using optimized query)
try {
    $categories = getActiveCategories($pdo);
} catch (Exception $e) {
    error_log("Categories error: " . $e->getMessage());
    $categories = [];
}

// Fetch pages with error handling (using optimized query)
try {
    $pages = getActivePagesWithCategories($pdo);
} catch (Exception $e) {
    error_log("Pages error: " . $e->getMessage());
    $pages = [];
}

// Prepare data for JavaScript
$menu_data = [
    'categories' => [],
    'pages' => []
];

foreach ($categories as $category) {
    $menu_data['categories'][] = [
        'id' => (int)$category['id'],
        'name_ar' => $category['name_ar'],
        'name_en' => $category['name_en']
    ];
}

foreach ($pages as $page) {
    $menu_data['pages'][] = [
        'id' => (int)$page['id'],
        'category' => (int)$page['category_id'],
        'image_ar' => SITE_URL . '/uploads/' . $page['image_ar'],
        'image_en' => $page['image_en'] ? SITE_URL . '/uploads/' . $page['image_en'] : SITE_URL . '/uploads/' . $page['image_ar'], // Fallback to AR image
        'responsive_images' => $page['responsive_images'] ?? []
    ];
}

$active_langs = explode(',', $settings['lang_active']);

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="<?php echo htmlspecialchars($settings['primary_color']); ?>">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <title><?php echo htmlspecialchars($settings['site_title']); ?></title>

    <style>
        :root {
            --primary: <?php echo htmlspecialchars($settings['primary_color']); ?>;
            --secondary: <?php echo htmlspecialchars($settings['secondary_color']); ?>;
            --button-bg: <?php echo htmlspecialchars($settings['button_bg_color'] ?? '#FFFFFF'); ?>;
            --text-color: <?php echo htmlspecialchars($settings['text_color'] ?? '#333333'); ?>;
            --language-btn-bg: <?php echo htmlspecialchars($settings['language_btn_bg'] ?? 'rgba(255, 255, 255, 0.85)'); ?>;
            --language-btn-text: <?php echo htmlspecialchars($settings['language_btn_text'] ?? '#333333'); ?>;
            --active-text-color: <?php echo htmlspecialchars($settings['active_text_color'] ?? '#FFFFFF'); ?>;
            --controls-height: 65px;
        }
        
        * { margin: 0; padding: 0; box-sizing: border-box; -webkit-tap-highlight-color: transparent; }
        html, body { height: 100%; overflow: hidden; font-family: 'Tajawal', sans-serif; background-color: #f5f5f5; }

        .page-wrapper {
            position: relative;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .menu-viewer {
            flex: 1;
            position: relative;
            overflow: hidden;
        }

        .menu-page {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            background: white;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: transform 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
        }

        .page-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            transition: opacity 0.3s;
        }

        /* تحسين عرض الصور للشاشات المختلفة */
        @media (orientation: portrait) {
            .page-image {
                object-fit: cover;
                object-position: center;
            }
        }

        @media (orientation: landscape) {
            .page-image {
                object-fit: cover;
                object-position: center;
            }
        }

        /* للشاشات الصغيرة جداً */
        @media (max-width: 375px) {
            .page-image {
                object-fit: cover;
                object-position: center;
            }
        }

        /* للشاشات الكبيرة */
        @media (min-width: 768px) {
            .page-image {
                object-fit: cover;
                object-position: center;
            }
        }

        .controls-container {
            height: var(--controls-height);
            flex-shrink: 0;
            background: var(--secondary);
            padding: 12px 8px;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: visible;
        }

        .category-menu {
            display: flex;
            overflow-x: auto;
            gap: 6px;
            scrollbar-width: none;
            padding: 0 4px;
            align-items: center;
        }
        .category-menu::-webkit-scrollbar { display: none; }

        .category-btn {
            padding: 8px 14px;
            background: var(--button-bg);
            border-radius: 18px;
            font-size: 13px;
            font-weight: 500;
            color: var(--text-color);
            white-space: nowrap;
            cursor: pointer;
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease-out;
            flex-shrink: 0;
            border: 2px solid var(--primary);
            margin: 2px;
        }

        .category-btn:hover {
            background: var(--primary);
            color: var(--active-text-color);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .category-btn.active {
            background: var(--primary);
            color: var(--active-text-color);
            border-color: var(--primary);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        /* Language Dropdown */
        .language-dropdown {
            position: absolute;
            top: 10px;
            z-index: 200;
        }
        html[dir="rtl"] .language-dropdown { left: 10px; }
        html[dir="ltr"] .language-dropdown { right: 10px; }

        .language-dropdown-toggle {
            background: var(--language-btn-bg);
            border: 1px solid rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            padding: 4px 12px;
            cursor: pointer;
            font-size: 12px;
            color: var(--language-btn-text);
            font-weight: 500;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .language-dropdown-toggle:hover {
            background: var(--primary);
            color: white;
            border-color: var(--primary);
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
        }

        .language-dropdown-menu {
            display: none;
            position: absolute;
            top: 100%;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
            list-style: none;
            padding: 3px 0;
            margin-top: 3px;
            min-width: 80px;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }
        html[dir="rtl"] .language-dropdown-menu { left: 0; }
        html[dir="ltr"] .language-dropdown-menu { right: 0; }

        .language-dropdown-menu a {
            display: block;
            padding: 8px 12px;
            color: var(--language-btn-text);
            text-decoration: none;
            transition: all 0.2s ease;
            font-weight: 500;
            font-size: 12px;
        }

        .language-dropdown-menu a:hover {
            background: var(--primary);
            color: white;
        }

        .language-dropdown-menu a.active {
            font-weight: bold;
            color: var(--primary);
            background: rgba(255, 255, 255, 0.9);
        }

        /* Enhanced Preloader */
        .preloader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            z-index: 9999;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .preloader.hidden {
            opacity: 0;
            pointer-events: none;
            transform: scale(1.1);
        }

        .spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            box-shadow: 0 0 20px rgba(255,255,255,0.3);
        }

        .preloader-text {
            margin-top: 25px;
            font-size: 1.1rem;
            color: #ffffff;
            font-weight: 500;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }



        /* Desktop view */
        /* Constrain aspect ratio on wide screens / landscape */
        @media (min-width: 700px) and (orientation: landscape) {
            body {
                justify-content: center;
                align-items: center;
            }
            .page-wrapper {
                width: 450px; /* Fixed width for landscape */
                height: 80vh; /* Fixed height */
                border: 1px solid #ddd;
                border-radius: 20px;
                overflow: hidden;
                box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            }
        }


    </style>
</head>
<body>

<div class="preloader" id="preloader">
    <div class="spinner"></div>
    <div class="preloader-text" id="preloaderText">جاري تحميل القائمة...</div>
</div>

<div class="page-wrapper">
    <?php if (count($active_langs) > 1): ?>
    <div class="language-dropdown" id="languageDropdown">
        <button class="language-dropdown-toggle" id="languageDropdownToggle">
            <?php echo $active_langs[0] === 'ar' ? 'English' : 'العربية'; ?>
        </button>
        <div class="language-dropdown-menu" id="languageDropdownMenu">
            <?php if (in_array('ar', $active_langs)): ?><a href="#" class="lang-link" data-lang="ar">العربية</a><?php endif; ?>
            <?php if (in_array('en', $active_langs)): ?><a href="#" class="lang-link" data-lang="en">English</a><?php endif; ?>
        </div>
    </div>
    <?php endif; ?>

    <div class="menu-viewer" id="menuViewer"></div>
    
    <div class="controls-container">
        <div class="category-menu" id="categoryMenu"></div>
    </div>
</div>

<script>
    const menuData = <?php echo json_encode($menu_data, JSON_UNESCAPED_UNICODE | JSON_NUMERIC_CHECK); ?>;
    const translations = {
        ar: { errorTitle: "خطأ بالتحميل", errorText: "تعذر تحميل الصفحة المطلوبة.", retry: "إعادة المحاولة" },
        en: { errorTitle: "Loading Error", errorText: "Failed to load the requested page.", retry: "Retry" }
    };

    let currentLang = localStorage.getItem('menuLang') || '<?php echo $active_langs[0]; ?>';
    let currentPageIndex = 0;
    let isDragging = false;
    let startX = 0;
    let currentTranslate = 0;

    const menuViewer = document.getElementById('menuViewer');
    const categoryMenu = document.getElementById('categoryMenu');

    document.addEventListener('DOMContentLoaded', init);

    async function init() {
        const preloader = document.getElementById('preloader');
        if (!menuData.pages || menuData.pages.length === 0) {
            menuViewer.innerHTML = '<p style="text-align:center; padding: 20px;">لا توجد صفحات لعرضها حالياً.</p>';
            preloader.classList.add('hidden');
            return;
        }

        createPages();
        createCategories();
        setupEventListeners();
        setupLanguageDropdown();
        setupResponsiveImageHandling();

        await preloadImages();
        
        updateView();
        document.documentElement.lang = currentLang;
        document.documentElement.dir = currentLang === 'ar' ? 'rtl' : 'ltr';
    }

    function createPages() {
        menuViewer.innerHTML = '';
        menuData.pages.forEach((page, index) => {
            const pageEl = document.createElement('div');
            pageEl.className = 'menu-page';
            pageEl.dataset.index = index;

            // Create image container for better mobile display
            const containerEl = document.createElement('div');
            containerEl.className = 'image-container';

            const imgEl = document.createElement('img');
            imgEl.className = 'page-image';
            imgEl.style.opacity = '0';
            imgEl.alt = `Page ${index + 1}`;

            containerEl.appendChild(imgEl);
            pageEl.appendChild(containerEl);
            menuViewer.appendChild(pageEl);
        });
    }

    function createCategories() {
        categoryMenu.innerHTML = '';
        menuData.categories.forEach(category => {
            const btn = document.createElement('div');
            btn.className = 'category-btn';
            btn.dataset.category = category.id;
            btn.addEventListener('click', () => {
                const firstPage = menuData.pages.findIndex(p => p.category === category.id);
                if (firstPage !== -1) goToPage(firstPage);
            });
            categoryMenu.appendChild(btn);
        });
        updateCategoryNames();
    }
    
    function updateCategoryNames() {
        document.querySelectorAll('.category-btn').forEach(btn => {
            const catId = parseInt(btn.dataset.category);
            const category = menuData.categories.find(c => c.id === catId);
            if(category) {
                btn.textContent = currentLang === 'ar' ? category.name_ar : category.name_en;
            }
        });
    }

    function setupEventListeners() {
        menuViewer.addEventListener('touchstart', dragStart, { passive: true });
        menuViewer.addEventListener('touchmove', drag, { passive: false });
        menuViewer.addEventListener('touchend', dragEnd);
        menuViewer.addEventListener('mousedown', dragStart);
        menuViewer.addEventListener('mousemove', drag);
        menuViewer.addEventListener('mouseup', dragEnd);
        menuViewer.addEventListener('mouseleave', dragEnd);
    }

    function setupResponsiveImageHandling() {
        // Window resize event to update responsive images
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                loadCurrentImage(); // Reload image with appropriate size
            }, 300);
        });

        // Orientation change event for mobile devices
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                loadCurrentImage();
            }, 500); // Wait for orientation change to complete
        });
    }

    function setupLanguageDropdown() {
        const dropdown = document.getElementById('languageDropdown');
        if (!dropdown) return;

        const toggle = document.getElementById('languageDropdownToggle');
        const menu = document.getElementById('languageDropdownMenu');
        const links = menu.querySelectorAll('.lang-link');

        function updateToggleText() {
            // إظهار اللغة الأخرى بدلاً من اللغة الحالية
            const otherLang = currentLang === 'ar' ? 'en' : 'ar';
            const otherLink = menu.querySelector(`.lang-link[data-lang="${otherLang}"]`);
            const activeLink = menu.querySelector(`.lang-link[data-lang="${currentLang}"]`);

            if (otherLink) {
                toggle.textContent = otherLink.textContent;
            }

            // تحديث الحالة النشطة
            links.forEach(l => l.classList.remove('active'));
            if (activeLink) {
                activeLink.classList.add('active');
            }
        }

        toggle.addEventListener('click', (e) => {
            e.stopPropagation();
            menu.style.display = menu.style.display === 'block' ? 'none' : 'block';
        });

        document.addEventListener('click', () => {
            menu.style.display = 'none';
        });

        links.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                currentLang = this.dataset.lang;
                localStorage.setItem('menuLang', currentLang);
                
                document.documentElement.lang = currentLang;
                document.documentElement.dir = currentLang === 'ar' ? 'rtl' : 'ltr';
                
                updateToggleText();
                updateCategoryNames();
                updateView(false);
            });
        });
        
        updateToggleText();
    }

    // Function to get responsive image URL based on screen size
    function getResponsiveImageUrl(pageData, lang) {
        const screenWidth = window.innerWidth;
        let sizeKey = '';

        // تبسيط الأحجام: 3 أحجام فقط - كلها طولية
        if (screenWidth <= 375) {
            sizeKey = 'small_phone';  // هاتف صغير (iPhone SE, etc)
        } else if (screenWidth <= 430) {
            sizeKey = 'large_phone';  // هاتف كبير (iPhone Pro Max, etc)
        } else {
            sizeKey = 'ipad';         // آيباد طولي
        }

        // Check if responsive image exists
        const responsiveKey = `${sizeKey}_${lang}`;
        if (pageData.responsive_images && pageData.responsive_images[responsiveKey]) {
            return 'uploads/responsive/' + pageData.responsive_images[responsiveKey];
        }

        // Fallback to original image
        return lang === 'en' && pageData.image_en ? pageData.image_en : pageData.image_ar;
    }

    function goToPage(index) {
        if (index < 0 || index >= menuData.pages.length) return;
        currentPageIndex = index;
        updateView();
    }

    function updateView(withTransition = true) {
        const pages = document.querySelectorAll('.menu-page');
        pages.forEach((page, index) => {
            page.style.transition = withTransition ? 'transform 0.25s cubic-bezier(0.4, 0.0, 0.2, 1)' : 'none';
            let offset = index - currentPageIndex;
            if (currentLang === 'ar') {
                offset = -offset;
            }
            page.style.transform = `translateX(${offset * 100}%)`;

            // Add active class to current page for enhanced styling
            if (index === currentPageIndex) {
                page.classList.add('active');
            } else {
                page.classList.remove('active');
            }
        });

        loadCurrentImage();
        updateActiveCategory();
        addPageTransitionEffect();
    }
    
    function preloadImages() {
        const preloader = document.getElementById('preloader');
        const preloaderText = document.getElementById('preloaderText');
        
        const imagesToLoad = [];
        menuData.pages.forEach(page => {
            if (page.image_ar) imagesToLoad.push(page.image_ar);
            if (page.image_en && page.image_en !== page.image_ar) imagesToLoad.push(page.image_en);
        });

        if (imagesToLoad.length === 0) {
            preloader.classList.add('hidden');
            return Promise.resolve();
        }

        const promises = imagesToLoad.map((src, index) => {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = () => {
                    preloaderText.textContent = `جاري تحميل الصور... (${index + 1}/${imagesToLoad.length})`;
                    resolve();
                };
                img.onerror = () => {
                    preloaderText.textContent = `جاري تحميل الصور... (${index + 1}/${imagesToLoad.length})`;
                    resolve(); // Resolve even on error to not block the app
                };
                img.src = src;
            });
        });

        return Promise.all(promises).then(() => {
            setTimeout(() => {
                preloader.classList.add('hidden');
            }, 300);
        });
    }

    function loadCurrentImage() {
        const pageElement = document.querySelector(`.menu-page[data-index="${currentPageIndex}"]`);
        if (!pageElement) return;

        const imgEl = pageElement.querySelector('.page-image');
        const pageData = menuData.pages[currentPageIndex];
        const imageUrl = getResponsiveImageUrl(pageData, currentLang);

        if (imgEl.src === imageUrl) return;

        // Add loading animation
        imgEl.style.opacity = '0';
        imgEl.style.transform = 'scale(1.05)';

        const img = new Image();
        img.src = imageUrl;
        img.onload = () => {
            imgEl.src = imageUrl;
            imgEl.style.opacity = '1';
            imgEl.style.transform = 'scale(1)';

            // Preload adjacent images for smoother navigation
            preloadAdjacentImages();
        };
        img.onerror = () => {
            imgEl.alt = translations[currentLang].errorText;
            imgEl.style.opacity = '0.5';
            imgEl.style.transform = 'scale(1)';
        };
    }

    // Preload adjacent images for smoother navigation
    function preloadAdjacentImages() {
        const preloadIndexes = [currentPageIndex - 1, currentPageIndex + 1];

        preloadIndexes.forEach(index => {
            if (index >= 0 && index < menuData.pages.length) {
                const pageData = menuData.pages[index];
                const imageUrl = currentLang === 'en' && pageData.image_en ? pageData.image_en : pageData.image_ar;

                const img = new Image();
                img.src = imageUrl;
            }
        });
    }

    function updateActiveCategory() {
        const currentCategory = menuData.pages[currentPageIndex]?.category;
        const categoryBtns = document.querySelectorAll('.category-btn');
        let activeBtn = null;
        categoryBtns.forEach(btn => {
            const isActive = parseInt(btn.dataset.category) === currentCategory;
            btn.classList.toggle('active', isActive);
            if (isActive) activeBtn = btn;
        });
        if (activeBtn) {
            activeBtn.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
        }
    }

    // Drag functions
    function dragStart(e) {
        isDragging = true;
        startX = getPositionX(e);
        menuViewer.style.cursor = 'grabbing';
    }

    function drag(e) {
        if (isDragging) {
            e.preventDefault();
            const currentX = getPositionX(e);
            currentTranslate = currentX - startX;
        }
    }

    function dragEnd() {
        if (!isDragging) return;
        isDragging = false;
        menuViewer.style.cursor = 'grab';

        const threshold = menuViewer.clientWidth / 8; // تقليل العتبة لتسريع التمرير
        const swipeLeft = currentTranslate < -threshold;
        const swipeRight = currentTranslate > threshold;
        const isRTL = currentLang === 'ar';

        if ((isRTL && swipeRight) || (!isRTL && swipeLeft)) {
            if (currentPageIndex < menuData.pages.length - 1) {
                currentPageIndex++;
            }
        } else if ((isRTL && swipeLeft) || (!isRTL && swipeRight)) {
            if (currentPageIndex > 0) {
                currentPageIndex--;
            }
        }
        
        currentTranslate = 0;
        updateView();
    }

    function getPositionX(e) {
        return e.type.includes('mouse') ? e.pageX : e.touches[0].clientX;
    }

    // Enhanced visual effects
    function addPageTransitionEffect() {
        // Add subtle vibration feedback on mobile devices
        if ('vibrate' in navigator && window.innerWidth <= 768) {
            navigator.vibrate(10);
        }

        // Add visual feedback to category buttons
        const activeBtn = document.querySelector('.category-btn.active');
        if (activeBtn) {
            activeBtn.style.transform = 'scale(1.05)';
            setTimeout(() => {
                activeBtn.style.transform = '';
            }, 200);
        }
    }

    // Add touch feedback for better mobile experience
    function addTouchFeedback() {
        const categoryBtns = document.querySelectorAll('.category-btn');
        categoryBtns.forEach(btn => {
            btn.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.95)';
            }, { passive: true });

            btn.addEventListener('touchend', function() {
                this.style.transform = '';
            }, { passive: true });
        });

        const langToggle = document.getElementById('languageDropdownToggle');
        if (langToggle) {
            langToggle.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.95)';
            }, { passive: true });

            langToggle.addEventListener('touchend', function() {
                this.style.transform = '';
            }, { passive: true });
        }
    }

    // Initialize touch feedback after DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(addTouchFeedback, 100);
    });
</script>
</body>
</html>