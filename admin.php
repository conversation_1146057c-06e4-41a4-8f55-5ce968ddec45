<?php
// Redirect to installer if config is missing
if (!file_exists('config.php')) {
    header('Location: install.php');
    exit;
}

require_once 'config.php';
require_once 'includes/security.php';

// Initialize secure session with error handling
try {
    initSecureSession();
} catch (Exception $e) {
    // Fallback to simple session if secure session fails
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
}

// Database connection with better error handling
try {
    $dbPath = __DIR__ . '/' . DB_FILE;

    if (!file_exists($dbPath)) {
        throw new Exception('ملف قاعدة البيانات غير موجود. يرجى إعادة تثبيت النظام.');
    }

    $pdo = new PDO('sqlite:' . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

    // Validate database structure with error handling
    try {
        if (!validateDatabaseConnection($dbPath)) {
            throw new Exception('قاعدة البيانات تالفة أو غير مكتملة.');
        }
    } catch (Exception $e) {
        // Log the error but don't stop execution if basic connection works
        error_log("Database validation warning: " . $e->getMessage());
    }

} catch (PDOException $e) {
    error_log("Admin database error: " . $e->getMessage());
    die("خطأ في الاتصال بقاعدة البيانات. يرجى المحاولة لاحقاً.");
} catch (Exception $e) {
    error_log("Admin system error: " . $e->getMessage());
    die($e->getMessage());
}

// Fetch settings for login check with error handling
try {
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM settings");
    $settings = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }

    if (!isset($settings['admin_user']) || !isset($settings['admin_pass'])) {
        throw new Exception('إعدادات المدير غير موجودة. يرجى إعادة تثبيت النظام.');
    }
} catch (Exception $e) {
    error_log("Settings fetch error: " . $e->getMessage());
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>خطأ في النظام</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
            .error-container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .error { color: #721c24; background: #f8d7da; padding: 15px; border-radius: 4px; border: 1px solid #f5c6cb; }
            .btn { display: inline-block; padding: 10px 20px; background: #8B4513; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        </style>
    </head>
    <body>
        <div class="error-container">
            <h2>❌ خطأ في تحميل إعدادات النظام</h2>
            <div class="error">
                <p><strong>التفاصيل:</strong> <?php echo htmlspecialchars($e->getMessage()); ?></p>
            </div>
            <h3>🔧 الحلول المقترحة:</h3>
            <p>
                <a href="debug_db.php" class="btn">🔍 فحص قاعدة البيانات</a>
                <a href="fix_db.php" class="btn">🔧 إصلاح قاعدة البيانات</a>
                <a href="simple_admin.php" class="btn">🔐 admin بسيط</a>
                <a href="install.php" class="btn">🚀 إعادة التنصيب</a>
            </p>
        </div>
    </body>
    </html>
    <?php
    exit;
}

$is_logged_in = isset($_SESSION['is_logged_in']) && $_SESSION['is_logged_in'] === true;
$login_error = '';

// Handle login
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
    $clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';

    // Basic validation first
    if (empty($username) || empty($password)) {
        $login_error = 'يرجى إدخال اسم المستخدم وكلمة المرور.';
    } else {
        // Try security checks with fallback
        $security_passed = true;
        $security_error = '';

        // Check CSRF token (with fallback)
        try {
            if (function_exists('verifyCSRFToken') && isset($_POST['csrf_token'])) {
                if (!verifyCSRFToken($_POST['csrf_token'])) {
                    $security_passed = false;
                    $security_error = 'رمز الأمان غير صحيح.';
                    if (function_exists('logSecurityEvent')) {
                        logSecurityEvent('CSRF_FAILED', 'Login attempt with invalid CSRF token');
                    }
                }
            }
        } catch (Exception $e) {
            // Continue without CSRF if function fails
            error_log("CSRF check failed: " . $e->getMessage());
        }

        // Check rate limiting (with fallback)
        try {
            if (function_exists('checkRateLimit') && $security_passed) {
                if (!checkRateLimit($clientIp)) {
                    $security_passed = false;
                    $security_error = 'تم تجاوز عدد المحاولات المسموح. يرجى المحاولة بعد 15 دقيقة.';
                    if (function_exists('logSecurityEvent')) {
                        logSecurityEvent('RATE_LIMIT_EXCEEDED', 'Too many login attempts');
                    }
                }
            }
        } catch (Exception $e) {
            // Continue without rate limiting if function fails
            error_log("Rate limit check failed: " . $e->getMessage());
        }

        // Sanitize input (with fallback)
        try {
            if (function_exists('sanitizeInput')) {
                $username = sanitizeInput($username);
            } else {
                $username = trim(strip_tags($username));
            }
        } catch (Exception $e) {
            $username = trim(strip_tags($username));
        }

        // Check credentials
        if ($security_passed) {
            if ($username === $settings['admin_user'] && password_verify($password, $settings['admin_pass'])) {
                $_SESSION['is_logged_in'] = true;

                // Reset rate limit if function exists
                try {
                    if (function_exists('resetRateLimit')) {
                        resetRateLimit($clientIp);
                    }
                    if (function_exists('logSecurityEvent')) {
                        logSecurityEvent('LOGIN_SUCCESS', "User: {$username}");
                    }
                } catch (Exception $e) {
                    error_log("Security logging failed: " . $e->getMessage());
                }

                header('Location: admin.php');
                exit;
            } else {
                $login_error = 'اسم المستخدم أو كلمة المرور غير صحيحة.';
                try {
                    if (function_exists('logSecurityEvent')) {
                        logSecurityEvent('LOGIN_FAILED', "Failed login attempt for user: {$username}");
                    }
                } catch (Exception $e) {
                    error_log("Security logging failed: " . $e->getMessage());
                }
            }
        } else {
            $login_error = $security_error;
        }
    }
}

// Handle logout
if (isset($_GET['action']) && $_GET['action'] === 'logout') {
    session_destroy();
    header('Location: admin.php');
    exit;
}

// If not logged in, show login form
if (!$is_logged_in) {
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - لوحة التحكم</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; background-color: #f0f2f5; display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; }
        .login-container { background: #fff; padding: 40px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); width: 360px; text-align: center; }
        h1 { color: #333; margin-bottom: 20px; }
        .form-group { margin-bottom: 20px; text-align: right; }
        label { display: block; margin-bottom: 5px; font-weight: 600; }
        input { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 5px; box-sizing: border-box; }
        .btn { background-color: #8B4513; color: #fff; padding: 12px; border: none; border-radius: 5px; cursor: pointer; width: 100%; font-size: 16px; }
        .btn:hover { background-color: #5D4037; }
        .error { color: #d93025; margin-top: 15px; }
    </style>
</head>
<body>
    <div class="login-container">
        <h1>لوحة التحكم</h1>
        <form method="POST">
            <?php
            // Generate CSRF token with fallback
            try {
                if (function_exists('generateCSRFToken')) {
                    echo '<input type="hidden" name="csrf_token" value="' . generateCSRFToken() . '">';
                } else {
                    // Simple fallback token
                    if (!isset($_SESSION['csrf_token'])) {
                        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
                    }
                    echo '<input type="hidden" name="csrf_token" value="' . $_SESSION['csrf_token'] . '">';
                }
            } catch (Exception $e) {
                // No CSRF token if generation fails
                error_log("CSRF token generation failed: " . $e->getMessage());
            }
            ?>
            <div class="form-group">
                <label for="username">اسم المستخدم</label>
                <input type="text" id="username" name="username" required>
            </div>
            <div class="form-group">
                <label for="password">كلمة المرور</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit" name="login" class="btn">تسجيل الدخول</button>
            <?php if ($login_error): ?>
                <p class="error"><?php echo htmlspecialchars($login_error); ?></p>
            <?php endif; ?>
        </form>
    </div>
</body>
</html>
<?php
    exit; // Stop execution for non-logged-in users
}

// --- ADMIN AREA ---
$page = $_GET['page'] ?? 'dashboard';
$page_title = 'لوحة التحكم';

// Create admin directory if it doesn't exist
if (!is_dir('admin')) {
    mkdir('admin', 0755, true);
}

// Simple router - we will create these files next
$allowed_pages = ['dashboard', 'categories', 'pages', 'settings', 'backup', 'performance', 'mobile-preview', 'responsive-images'];
if (!in_array($page, $allowed_pages)) {
    $page = 'dashboard';
}

// Define page titles
$titles = [
    'dashboard' => 'الرئيسية',
    'categories' => 'إدارة الأقسام',
    'pages' => 'إدارة الصفحات',
    'settings' => 'الإعدادات',
    'backup' => 'النسخ الاحتياطي',
    'performance' => 'الأداء والتحسين',
    'mobile-preview' => 'معاينة الهاتف المحمول',
    'responsive-images' => 'الصور المتجاوبة'
];
$page_title = $titles[$page];


// Placeholder for action handling (will be expanded)
$action_message = '';

// Include header
include 'admin/header.php';

// Include page content
if (file_exists("admin/{$page}.php")) {
    include "admin/{$page}.php";
} else {
    // Create a placeholder file if it doesn't exist
    $placeholder_content = "<?php\n// This is a placeholder file for the {$page} page.\n?>\n<h2>{$page_title}</h2>\n<p>محتوى الصفحة سيتم إضافته هنا.</p>";
    file_put_contents("admin/{$page}.php", $placeholder_content);
    include "admin/{$page}.php";
}

// Include footer
include 'admin/footer.php';

ob_end_flush(); // Flush the output buffer
?>