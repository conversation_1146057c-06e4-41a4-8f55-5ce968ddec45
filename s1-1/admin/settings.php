<?php
// Ensure the script is not accessed directly
if (!defined('DB_FILE')) {
    exit('Direct script access is not allowed.');
}

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Update general settings
    $settings_to_update = [
        'site_title' => $_POST['site_title'] ?? '',
        'primary_color' => $_POST['primary_color'] ?? '#8B4513',
        'secondary_color' => $_POST['secondary_color'] ?? '#FFFFFF',
        'button_bg_color' => $_POST['button_bg_color'] ?? '#FFFFFF',
    ];

    // Update active languages
    $active_langs = isset($_POST['active_langs']) && is_array($_POST['active_langs']) ? implode(',', $_POST['active_langs']) : '';
    $settings_to_update['lang_active'] = $active_langs;

    $stmt = $pdo->prepare("UPDATE settings SET setting_value = ? WHERE setting_key = ?");
    foreach ($settings_to_update as $key => $value) {
        $stmt->execute([$value, $key]);
    }

    // Update admin password if provided
    if (!empty($_POST['admin_pass'])) {
        if ($_POST['admin_pass'] === $_POST['admin_pass_confirm']) {
            $hashed_password = password_hash($_POST['admin_pass'], PASSWORD_DEFAULT);
            $stmt->execute([$hashed_password, 'admin_pass']);
        } else {
            $error = 'كلمتا المرور غير متطابقتين.';
        }
    }
    
    if (!$error) {
        $message = 'تم تحديث الإعدادات بنجاح!';
    }
    
    // Refresh settings variable
    $stmt = $pdo->query("SELECT * FROM settings");
    $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
}

$current_active_langs = explode(',', $settings['lang_active']);
?>

<?php if ($message): ?><div class="alert alert-success" style="margin-bottom: 20px;"><?php echo $message; ?></div><?php endif; ?>
<?php if ($error): ?><div class="alert alert-danger" style="margin-bottom: 20px;"><?php echo $error; ?></div><?php endif; ?>

<form method="POST" action="admin.php?page=settings">
    <h3>الإعدادات العامة</h3>
    <div class="form-group">
        <label for="site_title">عنوان الموقع</label>
        <input type="text" id="site_title" name="site_title" value="<?php echo htmlspecialchars($settings['site_title']); ?>" required>
    </div>
    <div class="form-group">
        <label for="primary_color">اللون الأساسي (للعناصر النشطة)</label>
        <input type="color" id="primary_color" name="primary_color" value="<?php echo htmlspecialchars($settings['primary_color']); ?>">
    </div>
    <div class="form-group">
        <label for="secondary_color">لون الشريط السفلي</label>
        <input type="color" id="secondary_color" name="secondary_color" value="<?php echo htmlspecialchars($settings['secondary_color']); ?>">
    </div>
    <div class="form-group">
        <label for="button_bg_color">لون خلفية أزرار الأقسام</label>
        <input type="color" id="button_bg_color" name="button_bg_color" value="<?php echo htmlspecialchars($settings['button_bg_color'] ?? '#FFFFFF'); ?>">
    </div>

    <hr style="margin: 30px 0;">

    <h3>إعدادات اللغة</h3>
    <div class="form-group">
        <label>اللغات المفعلة</label>
        <div>
            <label><input type="checkbox" name="active_langs[]" value="ar" <?php echo in_array('ar', $current_active_langs) ? 'checked' : ''; ?>> العربية</label>
        </div>
        <div>
            <label><input type="checkbox" name="active_langs[]" value="en" <?php echo in_array('en', $current_active_langs) ? 'checked' : ''; ?>> الإنجليزية</label>
        </div>
    </div>

    <hr style="margin: 30px 0;">

    <h3>تغيير كلمة المرور</h3>
    <div class="form-group">
        <label for="admin_pass">كلمة المرور الجديدة</label>
        <input type="password" id="admin_pass" name="admin_pass" placeholder="اتركها فارغة لعدم التغيير">
    </div>
    <div class="form-group">
        <label for="admin_pass_confirm">تأكيد كلمة المرور الجديدة</label>
        <input type="password" id="admin_pass_confirm" name="admin_pass_confirm">
    </div>

    <button type="submit" class="btn btn-primary">حفظ الإعدادات</button>
</form>