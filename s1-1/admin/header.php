<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
if (!isset($_SESSION['is_logged_in']) || $_SESSION['is_logged_in'] !== true) {
    header('Location: admin.php');
    exit;
}
global $page, $page_title;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title); ?> - لوحة التحكم</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #5D4037;
            --bg-color: #f8f9fa;
            --sidebar-bg: #343a40;
            --sidebar-text: #c2c7d0;
            --sidebar-active: #fff;
            --card-bg: #fff;
            --card-border: #dee2e6;
            --text-color: #333;
        }
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: var(--bg-color);
            margin: 0;
            display: flex;
            min-height: 100vh;
        }
        .sidebar {
            width: 250px;
            background-color: var(--sidebar-bg);
            color: var(--sidebar-text);
            display: flex;
            flex-direction: column;
            flex-shrink: 0;
        }
        .sidebar-header {
            padding: 20px;
            text-align: center;
            font-size: 1.2rem;
            font-weight: 700;
            color: #fff;
            border-bottom: 1px solid #4f5962;
        }
        .sidebar-nav {
            list-style: none;
            padding: 0;
            margin: 20px 0;
            flex-grow: 1;
        }
        .sidebar-nav a {
            display: block;
            padding: 15px 20px;
            color: var(--sidebar-text);
            text-decoration: none;
            transition: background-color 0.2s;
        }
        .sidebar-nav a:hover {
            background-color: #495057;
        }
        .sidebar-nav a.active {
            background-color: var(--primary-color);
            color: var(--sidebar-active);
            font-weight: 500;
        }
        .sidebar-footer {
            padding: 20px;
            text-align: center;
            border-top: 1px solid #4f5962;
        }
        .sidebar-footer a {
            color: var(--sidebar-text);
            text-decoration: none;
        }
        .main-content {
            flex-grow: 1;
            padding: 30px;
        }
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        .page-header h1 {
            margin: 0;
            color: var(--primary-color);
        }
        .card {
            background-color: var(--card-bg);
            border: 1px solid var(--card-border);
            border-radius: 8px;
            padding: 25px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            font-size: 15px;
            font-weight: 500;
            text-align: center;
            text-decoration: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.2s;
            border: none;
        }
        .btn-primary {
            background-color: var(--primary-color);
            color: #fff;
        }
        .btn-primary:hover {
            background-color: var(--secondary-color);
        }
        .btn-secondary {
            background-color: #6c757d;
            color: #fff;
        }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 8px; font-weight: 500; }
        input[type="text"], input[type="password"], input[type="email"], input[type="number"], select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            box-sizing: border-box;
        }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { padding: 12px; border: 1px solid #ddd; text-align: right; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <aside class="sidebar">
        <div class="sidebar-header">
            لوحة التحكم
        </div>
        <nav class="sidebar-nav">
            <a href="admin.php?page=dashboard" class="<?php echo $page === 'dashboard' ? 'active' : ''; ?>">الرئيسية</a>
            <a href="admin.php?page=categories" class="<?php echo $page === 'categories' ? 'active' : ''; ?>">الأقسام</a>
            <a href="admin.php?page=pages" class="<?php echo $page === 'pages' ? 'active' : ''; ?>">الصفحات</a>
            <a href="admin.php?page=settings" class="<?php echo $page === 'settings' ? 'active' : ''; ?>">الإعدادات</a>
        </nav>
        <div class="sidebar-footer">
            <a href="admin.php?action=logout">تسجيل الخروج</a>
        </div>
    </aside>
    <main class="main-content">
        <header class="page-header">
            <h1><?php echo htmlspecialchars($page_title); ?></h1>
            <a href="index.php" target="_blank" class="btn btn-secondary">عرض الموقع</a>
        </header>
        <div class="card">