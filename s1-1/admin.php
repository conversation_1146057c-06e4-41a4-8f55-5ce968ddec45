<?php
session_start();
ob_start(); // Start output buffering

// Redirect to installer if config is missing
if (!file_exists('config.php')) {
    header('Location: install.php');
    exit;
}

require_once 'config.php';

// Database connection
try {
    $pdo = new PDO('sqlite:' . __DIR__ . '/' . DB_FILE);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    die("فشل الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// Fetch settings for login check
$stmt = $pdo->query("SELECT * FROM settings");
$settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

$is_logged_in = isset($_SESSION['is_logged_in']) && $_SESSION['is_logged_in'] === true;
$login_error = '';

// Handle login
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
    $username = $_POST['username'];
    $password = $_POST['password'];

    if ($username === $settings['admin_user'] && password_verify($password, $settings['admin_pass'])) {
        $_SESSION['is_logged_in'] = true;
        header('Location: admin.php');
        exit;
    } else {
        $login_error = 'اسم المستخدم أو كلمة المرور غير صحيحة.';
    }
}

// Handle logout
if (isset($_GET['action']) && $_GET['action'] === 'logout') {
    session_destroy();
    header('Location: admin.php');
    exit;
}

// If not logged in, show login form
if (!$is_logged_in) {
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - لوحة التحكم</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; background-color: #f0f2f5; display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; }
        .login-container { background: #fff; padding: 40px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); width: 360px; text-align: center; }
        h1 { color: #333; margin-bottom: 20px; }
        .form-group { margin-bottom: 20px; text-align: right; }
        label { display: block; margin-bottom: 5px; font-weight: 600; }
        input { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 5px; box-sizing: border-box; }
        .btn { background-color: #8B4513; color: #fff; padding: 12px; border: none; border-radius: 5px; cursor: pointer; width: 100%; font-size: 16px; }
        .btn:hover { background-color: #5D4037; }
        .error { color: #d93025; margin-top: 15px; }
    </style>
</head>
<body>
    <div class="login-container">
        <h1>لوحة التحكم</h1>
        <form method="POST">
            <div class="form-group">
                <label for="username">اسم المستخدم</label>
                <input type="text" id="username" name="username" required>
            </div>
            <div class="form-group">
                <label for="password">كلمة المرور</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit" name="login" class="btn">تسجيل الدخول</button>
            <?php if ($login_error): ?>
                <p class="error"><?php echo $login_error; ?></p>
            <?php endif; ?>
        </form>
    </div>
</body>
</html>
<?php
    exit; // Stop execution for non-logged-in users
}

// --- ADMIN AREA ---
$page = $_GET['page'] ?? 'dashboard';
$page_title = 'لوحة التحكم';

// Create admin directory if it doesn't exist
if (!is_dir('admin')) {
    mkdir('admin', 0755, true);
}

// Simple router - we will create these files next
$allowed_pages = ['dashboard', 'categories', 'pages', 'settings'];
if (!in_array($page, $allowed_pages)) {
    $page = 'dashboard';
}

// Define page titles
$titles = [
    'dashboard' => 'الرئيسية',
    'categories' => 'إدارة الأقسام',
    'pages' => 'إدارة الصفحات',
    'settings' => 'الإعدادات'
];
$page_title = $titles[$page];


// Placeholder for action handling (will be expanded)
$action_message = '';

// Include header
include 'admin/header.php';

// Include page content
if (file_exists("admin/{$page}.php")) {
    include "admin/{$page}.php";
} else {
    // Create a placeholder file if it doesn't exist
    $placeholder_content = "<?php\n// This is a placeholder file for the {$page} page.\n?>\n<h2>{$page_title}</h2>\n<p>محتوى الصفحة سيتم إضافته هنا.</p>";
    file_put_contents("admin/{$page}.php", $placeholder_content);
    include "admin/{$page}.php";
}

// Include footer
include 'admin/footer.php';

ob_end_flush(); // Flush the output buffer
?>