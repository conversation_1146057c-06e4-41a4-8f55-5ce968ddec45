/**
 * Mobile Enhancements for SAA Menu System
 * Advanced features for better mobile experience
 */

// Performance monitoring
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            imageLoadTimes: [],
            navigationTimes: [],
            totalLoadTime: 0
        };
        this.startTime = performance.now();
    }
    
    recordImageLoad(loadTime) {
        this.metrics.imageLoadTimes.push(loadTime);
    }
    
    recordNavigation(navTime) {
        this.metrics.navigationTimes.push(navTime);
    }
    
    getAverageImageLoadTime() {
        const times = this.metrics.imageLoadTimes;
        return times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0;
    }
    
    logMetrics() {
        console.log('Performance Metrics:', {
            avgImageLoad: this.getAverageImageLoadTime().toFixed(2) + 'ms',
            totalImages: this.metrics.imageLoadTimes.length,
            totalNavigations: this.metrics.navigationTimes.length
        });
    }
}

// Image optimization and lazy loading
class ImageManager {
    constructor() {
        this.loadedImages = new Set();
        this.observer = null;
        this.initIntersectionObserver();
    }
    
    initIntersectionObserver() {
        if ('IntersectionObserver' in window) {
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadImage(entry.target);
                        this.observer.unobserve(entry.target);
                    }
                });
            }, {
                rootMargin: '50px'
            });
        }
    }
    
    loadImage(img) {
        const startTime = performance.now();
        const src = img.dataset.src || img.src;
        
        if (this.loadedImages.has(src)) return;
        
        img.classList.add('loading');
        
        const tempImg = new Image();
        tempImg.onload = () => {
            img.src = src;
            img.classList.remove('loading');
            img.classList.add('loaded');
            this.loadedImages.add(src);
            
            const loadTime = performance.now() - startTime;
            if (window.performanceMonitor) {
                window.performanceMonitor.recordImageLoad(loadTime);
            }
        };
        
        tempImg.onerror = () => {
            img.classList.remove('loading');
            img.classList.add('error');
            img.alt = 'فشل في تحميل الصورة';
        };
        
        tempImg.src = src;
    }
    
    observeImage(img) {
        if (this.observer) {
            this.observer.observe(img);
        } else {
            this.loadImage(img);
        }
    }
}

// Touch gesture handler
class TouchGestureHandler {
    constructor(element, callbacks) {
        this.element = element;
        this.callbacks = callbacks;
        this.touchStart = { x: 0, y: 0, time: 0 };
        this.touchEnd = { x: 0, y: 0, time: 0 };
        this.minSwipeDistance = 50;
        this.maxSwipeTime = 300;
        
        this.bindEvents();
    }
    
    bindEvents() {
        this.element.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true });
        this.element.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: true });
    }
    
    handleTouchStart(e) {
        const touch = e.touches[0];
        this.touchStart = {
            x: touch.clientX,
            y: touch.clientY,
            time: Date.now()
        };
    }
    
    handleTouchEnd(e) {
        const touch = e.changedTouches[0];
        this.touchEnd = {
            x: touch.clientX,
            y: touch.clientY,
            time: Date.now()
        };
        
        this.detectGesture();
    }
    
    detectGesture() {
        const deltaX = this.touchEnd.x - this.touchStart.x;
        const deltaY = this.touchEnd.y - this.touchStart.y;
        const deltaTime = this.touchEnd.time - this.touchStart.time;
        
        if (deltaTime > this.maxSwipeTime) return;
        
        const absX = Math.abs(deltaX);
        const absY = Math.abs(deltaY);
        
        if (absX > this.minSwipeDistance && absX > absY) {
            if (deltaX > 0) {
                this.callbacks.onSwipeRight && this.callbacks.onSwipeRight();
            } else {
                this.callbacks.onSwipeLeft && this.callbacks.onSwipeLeft();
            }
        } else if (absY > this.minSwipeDistance && absY > absX) {
            if (deltaY > 0) {
                this.callbacks.onSwipeDown && this.callbacks.onSwipeDown();
            } else {
                this.callbacks.onSwipeUp && this.callbacks.onSwipeUp();
            }
        }
    }
}

// Accessibility enhancements
class AccessibilityManager {
    constructor() {
        this.announcer = this.createAnnouncer();
    }
    
    createAnnouncer() {
        const announcer = document.createElement('div');
        announcer.setAttribute('aria-live', 'polite');
        announcer.setAttribute('aria-atomic', 'true');
        announcer.className = 'sr-only';
        document.body.appendChild(announcer);
        return announcer;
    }
    
    announce(message) {
        this.announcer.textContent = message;
        setTimeout(() => {
            this.announcer.textContent = '';
        }, 1000);
    }
    
    addSkipLink() {
        const skipLink = document.createElement('a');
        skipLink.href = '#main-content';
        skipLink.textContent = 'تخطي إلى المحتوى الرئيسي';
        skipLink.className = 'sr-only';
        skipLink.style.cssText = `
            position: absolute;
            top: -40px;
            left: 6px;
            background: #000;
            color: #fff;
            padding: 8px;
            text-decoration: none;
            z-index: 10000;
        `;
        
        skipLink.addEventListener('focus', () => {
            skipLink.style.top = '6px';
        });
        
        skipLink.addEventListener('blur', () => {
            skipLink.style.top = '-40px';
        });
        
        document.body.insertBefore(skipLink, document.body.firstChild);
    }
}

// Network status monitoring
class NetworkMonitor {
    constructor() {
        this.isOnline = navigator.onLine;
        this.bindEvents();
    }
    
    bindEvents() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.showNetworkStatus('متصل بالإنترنت', 'success');
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.showNetworkStatus('غير متصل بالإنترنت', 'error');
        });
    }
    
    showNetworkStatus(message, type) {
        const toast = document.createElement('div');
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: ${type === 'success' ? '#28a745' : '#dc3545'};
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            z-index: 10000;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(-50%) translateY(-20px)';
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 3000);
    }
}

// Initialize all enhancements
function initMobileEnhancements() {
    // Initialize performance monitoring
    window.performanceMonitor = new PerformanceMonitor();
    
    // Initialize image management
    window.imageManager = new ImageManager();
    
    // Initialize accessibility
    const accessibilityManager = new AccessibilityManager();
    accessibilityManager.addSkipLink();
    
    // Initialize network monitoring
    new NetworkMonitor();
    
    // Add main content ID for skip link
    const menuViewer = document.getElementById('menuViewer');
    if (menuViewer) {
        menuViewer.id = 'main-content';
        menuViewer.setAttribute('role', 'main');
        menuViewer.setAttribute('aria-label', 'عارض القائمة');
    }
    
    console.log('Mobile enhancements initialized');
}

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initMobileEnhancements);
} else {
    initMobileEnhancements();
}
