/**
 * SAA Menu System - Optimized JavaScript
 * Enhanced with performance optimizations and better UX
 */

class MenuSystem {
    constructor(menuData, translations, currentLang) {
        this.menuData = menuData;
        this.translations = translations;
        this.currentLang = currentLang;
        this.currentPageIndex = 0;
        this.currentCategoryIndex = 0;
        this.isTransitioning = false;
        
        // DOM elements
        this.menuViewer = document.getElementById('menuViewer');
        this.categoryMenu = document.getElementById('categoryMenu');
        this.preloader = document.getElementById('preloader');
        this.preloaderText = document.getElementById('preloaderText');
        
        // Touch handling
        this.touchStartX = 0;
        this.touchStartY = 0;
        this.touchEndX = 0;
        this.touchEndY = 0;
        this.minSwipeDistance = 50;
        
        // Performance optimization
        this.imageCache = new Map();
        this.intersectionObserver = null;
        
        this.init();
    }
    
    async init() {
        if (!this.menuData.pages || this.menuData.pages.length === 0) {
            this.showError('لا توجد صفحات لعرضها حالياً.');
            return;
        }
        
        this.createPages();
        this.createCategories();
        this.setupEventListeners();
        this.setupLanguageDropdown();
        this.setupIntersectionObserver();
        
        await this.preloadImages();
        
        this.updateView();
        this.updateDocumentLanguage();
        this.hidePreloader();
    }
    
    createPages() {
        this.menuViewer.innerHTML = '';
        
        this.menuData.pages.forEach((page, index) => {
            const pageEl = document.createElement('div');
            pageEl.className = 'menu-page';
            pageEl.dataset.index = index;
            
            // Create placeholder
            const placeholder = document.createElement('div');
            placeholder.className = 'image-placeholder';
            placeholder.innerHTML = '<i class="fas fa-image"></i>';
            
            // Create image element
            const imgEl = document.createElement('img');
            imgEl.className = 'page-image loading';
            imgEl.alt = `Page ${index + 1}`;
            imgEl.loading = 'lazy';
            imgEl.dataset.pageIndex = index;
            
            // Add event listeners
            imgEl.addEventListener('load', this.handleImageLoad.bind(this));
            imgEl.addEventListener('error', this.handleImageError.bind(this));
            
            pageEl.appendChild(placeholder);
            pageEl.appendChild(imgEl);
            this.menuViewer.appendChild(pageEl);
        });
    }
    
    createCategories() {
        if (!this.categoryMenu) return;
        
        this.categoryMenu.innerHTML = '';
        
        this.menuData.categories.forEach((category, index) => {
            const btn = document.createElement('button');
            btn.className = 'category-btn';
            btn.dataset.categoryId = category.id;
            btn.dataset.index = index;
            btn.textContent = this.currentLang === 'en' ? category.name_en : category.name_ar;
            
            btn.addEventListener('click', () => this.goToCategory(index));
            
            this.categoryMenu.appendChild(btn);
        });
    }
    
    setupEventListeners() {
        // Navigation buttons
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        
        if (prevBtn) prevBtn.addEventListener('click', () => this.previousPage());
        if (nextBtn) nextBtn.addEventListener('click', () => this.nextPage());
        
        // Touch events
        this.menuViewer.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true });
        this.menuViewer.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: true });
        
        // Keyboard navigation
        document.addEventListener('keydown', this.handleKeyDown.bind(this));
        
        // Window resize
        window.addEventListener('resize', this.debounce(this.handleResize.bind(this), 250));
    }
    
    setupLanguageDropdown() {
        const toggle = document.getElementById('languageDropdownToggle');
        const menu = document.getElementById('languageDropdownMenu');
        
        if (!toggle || !menu) return;
        
        const links = menu.querySelectorAll('.lang-link');
        
        toggle.addEventListener('click', (e) => {
            e.stopPropagation();
            menu.style.display = menu.style.display === 'block' ? 'none' : 'block';
        });
        
        document.addEventListener('click', () => {
            menu.style.display = 'none';
        });
        
        links.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                this.changeLanguage(link.dataset.lang);
            });
        });
        
        this.updateLanguageToggle();
    }
    
    setupIntersectionObserver() {
        if ('IntersectionObserver' in window) {
            this.intersectionObserver = new IntersectionObserver(
                this.handleIntersection.bind(this),
                { threshold: 0.1 }
            );
            
            // Observe all page images
            const images = this.menuViewer.querySelectorAll('.page-image');
            images.forEach(img => this.intersectionObserver.observe(img));
        }
    }
    
    handleIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                const pageIndex = parseInt(img.dataset.pageIndex);
                this.loadImageForPage(pageIndex);
            }
        });
    }
    
    async preloadImages() {
        const imagesToLoad = [];
        
        // Prioritize current and adjacent pages
        const priorityIndices = [
            this.currentPageIndex,
            this.currentPageIndex - 1,
            this.currentPageIndex + 1
        ].filter(i => i >= 0 && i < this.menuData.pages.length);
        
        priorityIndices.forEach(index => {
            const page = this.menuData.pages[index];
            if (page.image_ar) imagesToLoad.push({ url: page.image_ar, index });
            if (page.image_en && page.image_en !== page.image_ar) {
                imagesToLoad.push({ url: page.image_en, index });
            }
        });
        
        if (imagesToLoad.length === 0) return;
        
        const promises = imagesToLoad.map((imgData, i) => {
            return this.loadImageWithProgress(imgData.url, i, imagesToLoad.length);
        });
        
        await Promise.allSettled(promises);
    }
    
    loadImageWithProgress(url, index, total) {
        return new Promise((resolve) => {
            if (this.imageCache.has(url)) {
                resolve();
                return;
            }
            
            const img = new Image();
            img.onload = () => {
                this.imageCache.set(url, img);
                this.updatePreloaderText(index + 1, total);
                resolve();
            };
            img.onerror = () => {
                this.updatePreloaderText(index + 1, total);
                resolve();
            };
            img.src = url;
        });
    }
    
    updatePreloaderText(loaded, total) {
        if (this.preloaderText) {
            this.preloaderText.textContent = `جاري تحميل الصور... (${loaded}/${total})`;
        }
    }
    
    loadImageForPage(pageIndex) {
        const pageElement = this.menuViewer.querySelector(`[data-index="${pageIndex}"]`);
        if (!pageElement) return;
        
        const imgEl = pageElement.querySelector('.page-image');
        const placeholder = pageElement.querySelector('.image-placeholder');
        const pageData = this.menuData.pages[pageIndex];
        
        const imageUrl = this.currentLang === 'en' && pageData.image_en ? 
            pageData.image_en : pageData.image_ar;
        
        if (imgEl.src === imageUrl) return;
        
        // Check cache first
        if (this.imageCache.has(imageUrl)) {
            imgEl.src = imageUrl;
            this.handleImageLoad({ target: imgEl });
            return;
        }
        
        imgEl.classList.add('loading');
        placeholder.style.display = 'flex';
        imgEl.src = imageUrl;
    }
    
    handleImageLoad(event) {
        const img = event.target;
        img.classList.remove('loading');
        img.classList.add('loaded');
        
        const placeholder = img.parentElement.querySelector('.image-placeholder');
        if (placeholder) placeholder.style.display = 'none';
        
        // Cache the loaded image
        this.imageCache.set(img.src, img);
    }
    
    handleImageError(event) {
        const img = event.target;
        const placeholder = img.parentElement.querySelector('.image-placeholder');
        
        img.classList.remove('loading');
        img.style.display = 'none';
        
        if (placeholder) {
            placeholder.innerHTML = '<i class="fas fa-exclamation-triangle"></i><br>فشل تحميل الصورة';
            placeholder.style.display = 'flex';
        }
    }
    
    // Navigation methods
    nextPage() {
        if (this.isTransitioning) return;
        
        const nextIndex = this.currentPageIndex + 1;
        if (nextIndex < this.menuData.pages.length) {
            this.goToPage(nextIndex);
        }
    }
    
    previousPage() {
        if (this.isTransitioning) return;
        
        const prevIndex = this.currentPageIndex - 1;
        if (prevIndex >= 0) {
            this.goToPage(prevIndex);
        }
    }
    
    goToPage(index) {
        if (index < 0 || index >= this.menuData.pages.length || this.isTransitioning) return;
        
        this.isTransitioning = true;
        this.currentPageIndex = index;
        
        this.updateView();
        this.loadImageForPage(index);
        this.updateActiveCategory();
        
        // Preload adjacent images
        this.preloadAdjacentImages();
        
        setTimeout(() => {
            this.isTransitioning = false;
        }, 500);
    }
    
    goToCategory(categoryIndex) {
        const category = this.menuData.categories[categoryIndex];
        if (!category) return;
        
        const firstPageInCategory = this.menuData.pages.findIndex(
            page => page.category === category.id
        );
        
        if (firstPageInCategory !== -1) {
            this.currentCategoryIndex = categoryIndex;
            this.goToPage(firstPageInCategory);
        }
    }
    
    preloadAdjacentImages() {
        const adjacentIndices = [
            this.currentPageIndex - 1,
            this.currentPageIndex + 1
        ].filter(i => i >= 0 && i < this.menuData.pages.length);
        
        adjacentIndices.forEach(index => {
            setTimeout(() => this.loadImageForPage(index), 100);
        });
    }
    
    // Touch handling
    handleTouchStart(event) {
        this.touchStartX = event.touches[0].clientX;
        this.touchStartY = event.touches[0].clientY;
    }
    
    handleTouchEnd(event) {
        this.touchEndX = event.changedTouches[0].clientX;
        this.touchEndY = event.changedTouches[0].clientY;
        this.handleSwipe();
    }
    
    handleSwipe() {
        const deltaX = this.touchEndX - this.touchStartX;
        const deltaY = this.touchEndY - this.touchStartY;
        
        // Check if horizontal swipe is dominant
        if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > this.minSwipeDistance) {
            if (deltaX > 0) {
                this.previousPage();
            } else {
                this.nextPage();
            }
        }
    }
    
    // Keyboard navigation
    handleKeyDown(event) {
        switch (event.key) {
            case 'ArrowLeft':
                event.preventDefault();
                this.previousPage();
                break;
            case 'ArrowRight':
                event.preventDefault();
                this.nextPage();
                break;
        }
    }
    
    // Language handling
    changeLanguage(lang) {
        this.currentLang = lang;
        localStorage.setItem('menuLang', lang);
        
        this.updateDocumentLanguage();
        this.updateLanguageToggle();
        this.updateCategoryNames();
        this.updateView(false);
    }
    
    updateDocumentLanguage() {
        document.documentElement.lang = this.currentLang;
        document.documentElement.dir = this.currentLang === 'ar' ? 'rtl' : 'ltr';
    }
    
    updateLanguageToggle() {
        const toggle = document.getElementById('languageDropdownToggle');
        const menu = document.getElementById('languageDropdownMenu');
        
        if (!toggle || !menu) return;
        
        const activeLink = menu.querySelector(`[data-lang="${this.currentLang}"]`);
        if (activeLink) {
            toggle.textContent = activeLink.textContent;
            
            menu.querySelectorAll('.lang-link').forEach(l => l.classList.remove('active'));
            activeLink.classList.add('active');
        }
    }
    
    updateCategoryNames() {
        const categoryBtns = this.categoryMenu.querySelectorAll('.category-btn');
        categoryBtns.forEach((btn, index) => {
            const category = this.menuData.categories[index];
            if (category) {
                btn.textContent = this.currentLang === 'en' ? category.name_en : category.name_ar;
            }
        });
    }
    
    // UI updates
    updateView(animate = true) {
        this.updatePagePositions(animate);
        this.updateNavigationButtons();
        this.loadImageForPage(this.currentPageIndex);
    }
    
    updatePagePositions(animate = true) {
        const pages = this.menuViewer.querySelectorAll('.menu-page');
        
        pages.forEach((page, index) => {
            const offset = (index - this.currentPageIndex) * 100;
            const transform = `translateX(${offset}%)`;
            
            if (animate) {
                page.style.transition = 'transform 0.5s cubic-bezier(0.25, 0.8, 0.25, 1)';
            } else {
                page.style.transition = 'none';
            }
            
            page.style.transform = transform;
        });
    }
    
    updateNavigationButtons() {
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        
        if (prevBtn) {
            prevBtn.disabled = this.currentPageIndex === 0;
        }
        
        if (nextBtn) {
            nextBtn.disabled = this.currentPageIndex === this.menuData.pages.length - 1;
        }
    }
    
    updateActiveCategory() {
        const currentCategory = this.menuData.pages[this.currentPageIndex]?.category;
        const categoryBtns = this.categoryMenu.querySelectorAll('.category-btn');
        
        categoryBtns.forEach(btn => {
            btn.classList.toggle('active', parseInt(btn.dataset.categoryId) === currentCategory);
        });
    }
    
    // Utility methods
    showError(message) {
        this.menuViewer.innerHTML = `<p style="text-align:center; padding: 20px;">${message}</p>`;
        this.hidePreloader();
    }
    
    hidePreloader() {
        if (this.preloader) {
            setTimeout(() => {
                this.preloader.classList.add('hidden');
            }, 300);
        }
    }
    
    handleResize() {
        this.updateView(false);
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    if (typeof menuData !== 'undefined' && typeof translations !== 'undefined') {
        const savedLang = typeof initialLang !== 'undefined' ? initialLang : 'ar';
        window.menuSystem = new MenuSystem(menuData, translations, savedLang);
    }
});
