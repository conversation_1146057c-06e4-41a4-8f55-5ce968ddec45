/* SAA Menu System - Optimized CSS */

/* CSS Variables for theming */
:root {
    --primary: #8B4513;
    --secondary: #FFFFFF;
    --button-bg: #FFFFFF;
    --text-color: #333;
    --border-color: #ddd;
    --shadow: rgba(0, 0, 0, 0.1);
    --controls-height: 80px;
    --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--secondary);
    color: var(--text-color);
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    touch-action: manipulation;
    -webkit-user-select: none;
    user-select: none;
}

/* Preloader */
.preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--secondary);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s, visibility 0.5s;
}

.preloader.hidden {
    opacity: 0;
    visibility: hidden;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

.preloader-text {
    font-size: 16px;
    color: var(--text-color);
    text-align: center;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Language dropdown */
.language-dropdown {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.language-dropdown-toggle {
    background: var(--button-bg);
    border: 1px solid var(--border-color);
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    color: var(--text-color);
    box-shadow: 0 2px 10px var(--shadow);
    transition: var(--transition);
}

.language-dropdown-toggle:hover {
    background: var(--primary);
    color: white;
}

.language-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 4px 20px var(--shadow);
    display: none;
    min-width: 120px;
    margin-top: 5px;
}

.lang-link {
    display: block;
    padding: 10px 16px;
    text-decoration: none;
    color: var(--text-color);
    transition: var(--transition);
    border-bottom: 1px solid var(--border-color);
}

.lang-link:last-child {
    border-bottom: none;
}

.lang-link:hover,
.lang-link.active {
    background: var(--primary);
    color: white;
}

/* Page wrapper */
.page-wrapper {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Menu viewer */
.menu-viewer {
    flex: 1;
    position: relative;
    overflow: hidden;
}

.menu-page {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: white;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: transform 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* Image styles with optimization */
.page-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.3s, filter 0.3s;
    will-change: opacity, filter;
}

.page-image.loading {
    opacity: 0.5;
    filter: blur(5px);
}

.page-image.loaded {
    opacity: 1;
    filter: none;
}

.image-placeholder {
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 18px;
    z-index: 1;
}

.image-placeholder i {
    font-size: 48px;
    margin-bottom: 10px;
}

/* Controls container */
.controls-container {
    height: var(--controls-height);
    flex-shrink: 0;
    background: var(--secondary);
    padding: 10px;
    box-shadow: 0 -2px 10px var(--shadow);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Navigation buttons */
.nav-btn {
    background: var(--button-bg);
    border: 1px solid var(--border-color);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 20px;
    color: var(--text-color);
    transition: var(--transition);
    box-shadow: 0 2px 10px var(--shadow);
}

.nav-btn:hover:not(:disabled) {
    background: var(--primary);
    color: white;
    transform: scale(1.05);
}

.nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Category menu */
.category-menu {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    overflow-x: auto;
    padding: 0 10px;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.category-menu::-webkit-scrollbar {
    display: none;
}

.category-btn {
    background: var(--button-bg);
    border: 1px solid var(--border-color);
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    color: var(--text-color);
    white-space: nowrap;
    transition: var(--transition);
    box-shadow: 0 2px 5px var(--shadow);
    min-width: 80px;
    text-align: center;
}

.category-btn:hover,
.category-btn.active {
    background: var(--primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px var(--shadow);
}

/* Desktop responsive design */
@media (min-width: 700px) and (orientation: landscape) {
    body {
        justify-content: center;
        align-items: center;
        background: #f5f5f5;
    }
    
    .page-wrapper {
        width: 450px;
        height: 80vh;
        border: 1px solid var(--border-color);
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 10px 30px var(--shadow);
        background: white;
    }
}

/* Performance optimizations */
.menu-page {
    transform: translateZ(0);
    backface-visibility: hidden;
}

.page-image {
    transform: translateZ(0);
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000;
        --shadow: rgba(0, 0, 0, 0.5);
    }
}
