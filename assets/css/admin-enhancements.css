/* Admin Panel Enhancements */

/* Enhanced sidebar */
.sidebar {
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.sidebar-nav a {
    position: relative;
    overflow: hidden;
}

.sidebar-nav a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s;
}

.sidebar-nav a:hover::before {
    left: 100%;
}

/* Enhanced buttons */
.btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.btn:hover::before {
    width: 300px;
    height: 300px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Enhanced cards */
.card {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), #8B4513, var(--primary-color));
    transition: left 0.5s ease;
}

.card:hover::before {
    left: 100%;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

/* Enhanced form elements */
.form-group {
    position: relative;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
    outline: none;
}

.form-group label {
    transition: all 0.3s ease;
}

.form-group input:focus + label,
.form-group select:focus + label,
.form-group textarea:focus + label {
    color: var(--primary-color);
    transform: translateY(-2px);
}

/* Enhanced tables */
table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

tbody tr {
    transition: background-color 0.2s ease;
}

tbody tr:hover {
    background-color: rgba(139, 69, 19, 0.05);
}

th {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

/* Loading states */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translate(-50%, -50%);
    z-index: 1;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Success/Error messages */
.message {
    padding: 15px 20px;
    border-radius: 8px;
    margin: 20px 0;
    position: relative;
    animation: slideIn 0.3s ease;
}

.message.success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border: 1px solid #c3e6cb;
    color: #155724;
}

.message.error {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.message.info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    border: 1px solid #bee5eb;
    color: #0c5460;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    body {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        order: 2;
    }
    
    .sidebar-nav {
        display: flex;
        overflow-x: auto;
        margin: 10px 0;
    }
    
    .sidebar-nav a {
        white-space: nowrap;
        padding: 10px 15px;
        font-size: 14px;
    }
    
    .main-content {
        order: 1;
        padding: 20px 15px;
    }
    
    .page-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .card {
        padding: 20px 15px;
    }
    
    table {
        font-size: 14px;
    }
    
    th, td {
        padding: 8px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #1a1a1a;
        --card-bg: #2d2d2d;
        --card-border: #404040;
        --text-color: #e0e0e0;
    }
    
    body {
        color: var(--text-color);
    }
    
    .card {
        background-color: var(--card-bg);
        border-color: var(--card-border);
    }
    
    input, select, textarea {
        background-color: var(--card-bg);
        color: var(--text-color);
        border-color: var(--card-border);
    }
    
    th {
        background: linear-gradient(135deg, #404040, #505050);
        color: var(--text-color);
    }
    
    tbody tr:hover {
        background-color: rgba(139, 69, 19, 0.2);
    }
}

/* Print styles */
@media print {
    .sidebar {
        display: none;
    }
    
    .main-content {
        padding: 0;
    }
    
    .btn {
        display: none;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ccc;
    }
}
