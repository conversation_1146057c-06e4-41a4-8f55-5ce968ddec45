/* Mobile Enhancements for SAA Menu System */

/* Enhanced touch interactions */
.touch-feedback {
    -webkit-tap-highlight-color: rgba(139, 69, 19, 0.2);
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
}

/* Improved image loading states */
.page-image {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.page-image.loading {
    opacity: 0.7;
    transform: scale(1.02);
}

.page-image.loaded {
    opacity: 1;
    transform: scale(1);
}

/* Enhanced button states */
.category-btn {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}

.category-btn:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

/* Better accessibility for screen readers */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Loading skeleton for better perceived performance */
.image-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Enhanced focus states for keyboard navigation */
.language-dropdown-toggle:focus,
.category-btn:focus {
    box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.3);
}

/* Smooth scrolling for category menu */
.category-menu {
    scroll-snap-type: x mandatory;
}

.category-btn {
    scroll-snap-align: center;
}

/* Better error states */
.error-image {
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 14px;
    text-align: center;
    padding: 20px;
}

/* Improved preloader for mobile */
@media (max-width: 768px) {
    .preloader {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .spinner {
        width: 50px;
        height: 50px;
    }
    
    .preloader-text {
        font-size: 1rem;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .category-btn {
        border: 2px solid currentColor;
    }
    
    .category-btn.active {
        background: #000;
        color: #fff;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .menu-page,
    .page-image,
    .category-btn,
    .preloader {
        transition: none;
        animation: none;
    }
    
    .spinner {
        animation: none;
        border: 4px solid #ccc;
        border-top: 4px solid var(--primary);
    }
}

/* Dark mode support (if implemented) */
@media (prefers-color-scheme: dark) {
    .menu-page {
        background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    }
    
    .page-image {
        background: #2d3748;
    }
    
    .category-btn {
        background: #4a5568;
        color: #e2e8f0;
        border-color: #718096;
    }
    
    .category-btn.active {
        background: linear-gradient(135deg, #8B4513, #654321);
    }
    
    .language-dropdown-toggle {
        background: rgba(45, 55, 72, 0.95);
        color: #e2e8f0;
        border-color: #718096;
    }
    
    .language-dropdown-menu {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .language-dropdown-menu a {
        color: #e2e8f0;
    }
    
    .language-dropdown-menu a:hover {
        background: #4a5568;
    }
}

/* Print styles (hide interactive elements) */
@media print {
    .controls-container,
    .language-dropdown,
    .preloader {
        display: none !important;
    }
    
    .menu-page {
        position: static;
        page-break-inside: avoid;
    }
    
    .page-image {
        max-width: 100%;
        height: auto;
    }
}
