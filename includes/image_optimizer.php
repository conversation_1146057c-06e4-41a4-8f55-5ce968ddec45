<?php
/**
 * Image Optimization and Processing Functions
 * Enhanced image handling with multiple sizes and optimization
 */

// Prevent direct access
if (!defined('DB_FILE')) {
    exit('Direct access not allowed');
}

class ImageOptimizer {
    
    private $uploadDir;
    private $thumbsDir;
    private $mediumDir;
    
    // Image size configurations - Enhanced with more sizes
    private $sizes = [
        'thumb' => ['width' => 150, 'height' => 150, 'quality' => 75],
        'small' => ['width' => 300, 'height' => 300, 'quality' => 80],
        'medium' => ['width' => 600, 'height' => 600, 'quality' => 85],
        'large' => ['width' => 1200, 'height' => 1200, 'quality' => 90],
        'xlarge' => ['width' => 1920, 'height' => 1920, 'quality' => 92]
    ];

    // WebP support flag
    private $webpSupport = false;
    
    public function __construct($uploadDir) {
        $this->uploadDir = rtrim($uploadDir, '/') . '/';
        $this->thumbsDir = $this->uploadDir . 'thumbs/';
        $this->mediumDir = $this->uploadDir . 'medium/';

        // Check WebP support
        $this->webpSupport = function_exists('imagewebp');

        // Create directories if they don't exist
        $this->createDirectories();
    }
    
    private function createDirectories() {
        $dirs = [$this->uploadDir, $this->thumbsDir, $this->mediumDir];
        
        foreach ($dirs as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
                
                // Add .htaccess to prevent script execution
                $htaccess = $dir . '.htaccess';
                if (!file_exists($htaccess)) {
                    file_put_contents($htaccess, "# Prevent script execution\n<Files \"*.php\">\n    Order allow,deny\n    Deny from all\n</Files>");
                }
            }
        }
    }
    
    /**
     * Process uploaded image and create multiple sizes
     */
    public function processImage($sourceFile, $filename) {
        $results = [];
        
        try {
            // Get image info
            $imageInfo = getimagesize($sourceFile);
            if (!$imageInfo) {
                throw new Exception('Invalid image file');
            }
            
            $originalWidth = $imageInfo[0];
            $originalHeight = $imageInfo[1];
            $mimeType = $imageInfo['mime'];
            
            // Create image resource from source
            $sourceImage = $this->createImageFromFile($sourceFile, $mimeType);
            if (!$sourceImage) {
                throw new Exception('Failed to create image resource');
            }
            
            // Save original (optimized)
            $originalPath = $this->uploadDir . $filename;
            $this->saveOptimizedImage($sourceImage, $originalPath, $originalWidth, $originalHeight, $this->sizes['large']['quality']);
            $results['original'] = $filename;
            
            // Create thumbnail
            $thumbFilename = 'thumb_' . $filename;
            $thumbPath = $this->thumbsDir . $thumbFilename;
            $this->resizeAndSave($sourceImage, $thumbPath, $originalWidth, $originalHeight, 
                               $this->sizes['thumb']['width'], $this->sizes['thumb']['height'], 
                               $this->sizes['thumb']['quality']);
            $results['thumb'] = $thumbFilename;
            
            // Create medium size
            $mediumFilename = 'medium_' . $filename;
            $mediumPath = $this->mediumDir . $mediumFilename;
            $this->resizeAndSave($sourceImage, $mediumPath, $originalWidth, $originalHeight, 
                               $this->sizes['medium']['width'], $this->sizes['medium']['height'], 
                               $this->sizes['medium']['quality']);
            $results['medium'] = $mediumFilename;
            
            // Clean up
            imagedestroy($sourceImage);
            
            return $results;
            
        } catch (Exception $e) {
            throw new Exception('Image processing failed: ' . $e->getMessage());
        }
    }
    
    private function createImageFromFile($file, $mimeType) {
        switch ($mimeType) {
            case 'image/jpeg':
                return imagecreatefromjpeg($file);
            case 'image/png':
                return imagecreatefrompng($file);
            case 'image/gif':
                return imagecreatefromgif($file);
            default:
                return false;
        }
    }
    
    private function resizeAndSave($sourceImage, $targetPath, $originalWidth, $originalHeight, $targetWidth, $targetHeight, $quality) {
        // Calculate new dimensions maintaining aspect ratio
        $ratio = min($targetWidth / $originalWidth, $targetHeight / $originalHeight);
        $newWidth = round($originalWidth * $ratio);
        $newHeight = round($originalHeight * $ratio);
        
        // Create new image
        $newImage = imagecreatetruecolor($newWidth, $newHeight);
        
        // Preserve transparency for PNG and GIF
        imagealphablending($newImage, false);
        imagesavealpha($newImage, true);
        $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
        imagefilledrectangle($newImage, 0, 0, $newWidth, $newHeight, $transparent);
        
        // Resize image
        imagecopyresampled($newImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight);
        
        // Save image
        $this->saveOptimizedImage($newImage, $targetPath, $newWidth, $newHeight, $quality);
        
        // Clean up
        imagedestroy($newImage);
    }
    
    private function saveOptimizedImage($image, $path, $width, $height, $quality) {
        $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
        
        switch ($extension) {
            case 'jpg':
            case 'jpeg':
                imagejpeg($image, $path, $quality);
                break;
            case 'png':
                // PNG quality is 0-9, convert from 0-100
                $pngQuality = round((100 - $quality) / 10);
                imagepng($image, $path, $pngQuality);
                break;
            case 'gif':
                imagegif($image, $path);
                break;
        }
        
        // Set proper permissions
        chmod($path, 0644);
    }
    
    /**
     * Delete all sizes of an image
     */
    public function deleteImage($filename) {
        $files = [
            $this->uploadDir . $filename,
            $this->thumbsDir . 'thumb_' . $filename,
            $this->mediumDir . 'medium_' . $filename
        ];
        
        foreach ($files as $file) {
            if (file_exists($file)) {
                unlink($file);
            }
        }
    }
    
    /**
     * Get image URL for specific size
     */
    public function getImageUrl($filename, $size = 'original') {
        $baseUrl = SITE_URL . '/uploads/';
        
        switch ($size) {
            case 'thumb':
                return $baseUrl . 'thumbs/thumb_' . $filename;
            case 'medium':
                return $baseUrl . 'medium/medium_' . $filename;
            case 'original':
            default:
                return $baseUrl . $filename;
        }
    }
    
    /**
     * Check if image exists in specific size
     */
    public function imageExists($filename, $size = 'original') {
        switch ($size) {
            case 'thumb':
                return file_exists($this->thumbsDir . 'thumb_' . $filename);
            case 'medium':
                return file_exists($this->mediumDir . 'medium_' . $filename);
            case 'original':
            default:
                return file_exists($this->uploadDir . $filename);
        }
    }
    
    /**
     * Get image dimensions
     */
    public function getImageDimensions($filename, $size = 'original') {
        $path = '';
        switch ($size) {
            case 'thumb':
                $path = $this->thumbsDir . 'thumb_' . $filename;
                break;
            case 'medium':
                $path = $this->mediumDir . 'medium_' . $filename;
                break;
            case 'original':
            default:
                $path = $this->uploadDir . $filename;
                break;
        }
        
        if (file_exists($path)) {
            $info = getimagesize($path);
            return ['width' => $info[0], 'height' => $info[1]];
        }
        
        return false;
    }
}

// Global function for easy access
function getImageOptimizer() {
    static $optimizer = null;
    if ($optimizer === null) {
        $uploadDir = __DIR__ . '/../uploads/';
        $optimizer = new ImageOptimizer($uploadDir);
    }
    return $optimizer;
}
