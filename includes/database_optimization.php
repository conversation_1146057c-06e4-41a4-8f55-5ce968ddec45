<?php
/**
 * Advanced Database Optimization for SAA Menu System
 * Handles database indexes, caching, and performance improvements
 */

// Prevent direct access
if (!defined('DB_FILE')) {
    exit('Direct access not allowed');
}

// Load cache manager
require_once __DIR__ . '/cache_manager.php';

class DatabaseOptimizer {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * Create database indexes for better performance
     */
    public function createIndexes() {
        $indexes = [
            // Categories table indexes
            'CREATE INDEX IF NOT EXISTS idx_categories_active ON categories(is_active)',
            'CREATE INDEX IF NOT EXISTS idx_categories_sort ON categories(sort_order)',
            'CREATE INDEX IF NOT EXISTS idx_categories_active_sort ON categories(is_active, sort_order)',
            
            // Pages table indexes
            'CREATE INDEX IF NOT EXISTS idx_pages_active ON pages(is_active)',
            'CREATE INDEX IF NOT EXISTS idx_pages_category ON pages(category_id)',
            'CREATE INDEX IF NOT EXISTS idx_pages_sort ON pages(sort_order)',
            'CREATE INDEX IF NOT EXISTS idx_pages_active_category ON pages(is_active, category_id)',
            'CREATE INDEX IF NOT EXISTS idx_pages_category_sort ON pages(category_id, sort_order)',
            'CREATE INDEX IF NOT EXISTS idx_pages_active_category_sort ON pages(is_active, category_id, sort_order)',
            
            // Settings table indexes
            'CREATE INDEX IF NOT EXISTS idx_settings_key ON settings(setting_key)',
        ];
        
        try {
            foreach ($indexes as $indexQuery) {
                $this->pdo->exec($indexQuery);
            }
            return true;
        } catch (PDOException $e) {
            error_log("Index creation error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Analyze database performance
     */
    public function analyzeDatabase() {
        $analysis = [];
        
        try {
            // Get table sizes
            $tables = ['categories', 'pages', 'settings'];
            foreach ($tables as $table) {
                $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM {$table}");
                $result = $stmt->fetch();
                $analysis['table_sizes'][$table] = $result['count'];
            }
            
            // Check for unused indexes
            $stmt = $this->pdo->query("PRAGMA index_list('categories')");
            $analysis['indexes']['categories'] = $stmt->fetchAll();
            
            $stmt = $this->pdo->query("PRAGMA index_list('pages')");
            $analysis['indexes']['pages'] = $stmt->fetchAll();
            
            $stmt = $this->pdo->query("PRAGMA index_list('settings')");
            $analysis['indexes']['settings'] = $stmt->fetchAll();
            
            // Database file size
            if (file_exists(DB_FILE)) {
                $analysis['database_size'] = filesize(DB_FILE);
            }
            
            return $analysis;
            
        } catch (PDOException $e) {
            error_log("Database analysis error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Optimize database (VACUUM and ANALYZE)
     */
    public function optimizeDatabase() {
        try {
            // VACUUM to reclaim space and defragment
            $this->pdo->exec('VACUUM');
            
            // ANALYZE to update statistics
            $this->pdo->exec('ANALYZE');
            
            return true;
        } catch (PDOException $e) {
            error_log("Database optimization error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get slow queries (for future monitoring)
     */
    public function getSlowQueries() {
        // This would be implemented with query logging in a production system
        // For now, return common queries that might be slow
        return [
            'SELECT p.* FROM pages p JOIN categories c ON p.category_id = c.id WHERE p.is_active = 1 AND c.is_active = 1 ORDER BY c.sort_order ASC, p.sort_order ASC',
            'SELECT * FROM categories WHERE is_active = 1 ORDER BY sort_order ASC',
            'SELECT * FROM pages WHERE category_id = ? AND is_active = 1 ORDER BY sort_order ASC'
        ];
    }
    
    /**
     * Check database integrity
     */
    public function checkIntegrity() {
        try {
            $stmt = $this->pdo->query('PRAGMA integrity_check');
            $result = $stmt->fetch();
            
            return $result['integrity_check'] === 'ok';
        } catch (PDOException $e) {
            error_log("Integrity check error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get database statistics
     */
    public function getDatabaseStats() {
        $stats = [];
        
        try {
            // Page count
            $stmt = $this->pdo->query('PRAGMA page_count');
            $result = $stmt->fetch();
            $stats['page_count'] = $result['page_count'];
            
            // Page size
            $stmt = $this->pdo->query('PRAGMA page_size');
            $result = $stmt->fetch();
            $stats['page_size'] = $result['page_size'];
            
            // Free pages
            $stmt = $this->pdo->query('PRAGMA freelist_count');
            $result = $stmt->fetch();
            $stats['free_pages'] = $result['freelist_count'];
            
            // Calculate database efficiency
            $stats['efficiency'] = $stats['page_count'] > 0 ? 
                (($stats['page_count'] - $stats['free_pages']) / $stats['page_count']) * 100 : 100;
            
            return $stats;
        } catch (PDOException $e) {
            error_log("Database stats error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Optimize specific queries
     */
    public function getOptimizedQueries() {
        return [
            'active_categories' => 'SELECT * FROM categories WHERE is_active = 1 ORDER BY sort_order ASC',
            'active_pages_by_category' => 'SELECT p.* FROM pages p INNER JOIN categories c ON p.category_id = c.id WHERE p.is_active = 1 AND c.is_active = 1 ORDER BY c.sort_order ASC, p.sort_order ASC',
            'category_pages' => 'SELECT * FROM pages WHERE category_id = ? AND is_active = 1 ORDER BY sort_order ASC',
            'settings_by_key' => 'SELECT setting_value FROM settings WHERE setting_key = ?'
        ];
    }
}

/**
 * Initialize database optimization
 */
function initDatabaseOptimization($pdo) {
    $optimizer = new DatabaseOptimizer($pdo);
    
    // Create indexes if they don't exist
    $optimizer->createIndexes();
    
    return $optimizer;
}

/**
 * Cached query helper
 */
class QueryCache {
    private static $cache = [];
    private static $maxCacheSize = 50;
    
    public static function get($key) {
        return isset(self::$cache[$key]) ? self::$cache[$key] : null;
    }
    
    public static function set($key, $value) {
        if (count(self::$cache) >= self::$maxCacheSize) {
            // Remove oldest entry
            array_shift(self::$cache);
        }
        self::$cache[$key] = $value;
    }
    
    public static function clear() {
        self::$cache = [];
    }
}

/**
 * Optimized database queries
 */
function getActiveCategories($pdo, $useAdvancedCache = true) {
    // Try advanced cache first
    if ($useAdvancedCache) {
        $cache = new CacheManager();
        $cacheKey = 'active_categories_v2';

        if ($cache->has($cacheKey)) {
            return $cache->get($cacheKey);
        }
    }

    // Fallback to simple cache
    $cacheKey = 'active_categories';
    $cached = QueryCache::get($cacheKey);

    if ($cached !== null) {
        return $cached;
    }

    $startTime = microtime(true);
    $stmt = $pdo->query('SELECT * FROM categories WHERE is_active = 1 ORDER BY sort_order ASC');
    $result = $stmt->fetchAll();
    $executionTime = microtime(true) - $startTime;

    // Log performance
    PerformanceMonitor::logQuery("SELECT categories WHERE is_active = 1", $executionTime);

    // Cache results
    QueryCache::set($cacheKey, $result);

    if ($useAdvancedCache) {
        $cache = new CacheManager();
        $cache->set('active_categories_v2', $result, 1800); // 30 minutes
    }

    return $result;
}

function getActivePagesWithCategories($pdo) {
    $cacheKey = 'active_pages_with_categories_v2';
    $cached = QueryCache::get($cacheKey);

    if ($cached !== null) {
        return $cached;
    }

    // Get pages with categories
    $stmt = $pdo->query('SELECT p.* FROM pages p INNER JOIN categories c ON p.category_id = c.id WHERE p.is_active = 1 AND c.is_active = 1 ORDER BY c.sort_order ASC, p.sort_order ASC');
    $pages = $stmt->fetchAll();

    // Get responsive images for all pages
    $responsiveImages = [];
    if (!empty($pages)) {
        $pageIds = array_column($pages, 'id');
        $placeholders = str_repeat('?,', count($pageIds) - 1) . '?';
        $stmt = $pdo->prepare("SELECT page_id, size_lang, filename FROM responsive_images WHERE page_id IN ($placeholders)");
        $stmt->execute($pageIds);

        while ($row = $stmt->fetch()) {
            $responsiveImages[$row['page_id']][$row['size_lang']] = $row['filename'];
        }
    }

    // Add responsive images to pages
    foreach ($pages as &$page) {
        $page['responsive_images'] = $responsiveImages[$page['id']] ?? [];
    }

    QueryCache::set($cacheKey, $pages);
    return $pages;
}

function getCategoryPages($pdo, $categoryId) {
    $cacheKey = 'category_pages_' . $categoryId;
    $cached = QueryCache::get($cacheKey);
    
    if ($cached !== null) {
        return $cached;
    }
    
    $stmt = $pdo->prepare('SELECT * FROM pages WHERE category_id = ? AND is_active = 1 ORDER BY sort_order ASC');
    $stmt->execute([$categoryId]);
    $result = $stmt->fetchAll();
    
    QueryCache::set($cacheKey, $result);
    return $result;
}

function getSettingValue($pdo, $key) {
    $cacheKey = 'setting_' . $key;
    $cached = QueryCache::get($cacheKey);
    
    if ($cached !== null) {
        return $cached;
    }
    
    $stmt = $pdo->prepare('SELECT setting_value FROM settings WHERE setting_key = ?');
    $stmt->execute([$key]);
    $result = $stmt->fetchColumn();
    
    QueryCache::set($cacheKey, $result);
    return $result;
}
