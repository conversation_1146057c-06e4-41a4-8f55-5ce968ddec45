<?php
/**
 * Backup System for SAA Menu System
 * Handles automatic and manual backups
 */

// Prevent direct access
if (!defined('DB_FILE')) {
    exit('Direct access not allowed');
}

class BackupManager {
    private $backupDir = 'backups/';
    private $maxBackups = 10;
    private $dbFile;
    
    public function __construct($dbFile) {
        $this->dbFile = $dbFile;
        $this->createBackupDirectory();
    }
    
    /**
     * Create backup directory if it doesn't exist
     */
    private function createBackupDirectory() {
        if (!is_dir($this->backupDir)) {
            mkdir($this->backupDir, 0755, true);
        }
        
        // Create .htaccess to protect backups
        $htaccessFile = $this->backupDir . '.htaccess';
        if (!file_exists($htaccessFile)) {
            $content = "Order allow,deny\nDeny from all\n";
            file_put_contents($htaccessFile, $content);
        }
    }
    
    /**
     * Create a full backup (database + uploads)
     */
    public function createFullBackup() {
        try {
            $timestamp = date('Y-m-d_H-i-s');
            $backupName = "backup_{$timestamp}";
            $backupPath = $this->backupDir . $backupName;
            
            // Create backup directory
            mkdir($backupPath, 0755, true);
            
            // Backup database
            $this->backupDatabase($backupPath . '/database.db');
            
            // Backup uploads
            $this->backupUploads($backupPath . '/uploads');
            
            // Backup config
            if (file_exists('config.php')) {
                copy('config.php', $backupPath . '/config.php');
            }
            
            // Create backup info file
            $this->createBackupInfo($backupPath, $timestamp);
            
            // Clean old backups
            $this->cleanOldBackups();
            
            return $backupName;
            
        } catch (Exception $e) {
            error_log("Backup error: " . $e->getMessage());
            throw new Exception("فشل في إنشاء النسخة الاحتياطية: " . $e->getMessage());
        }
    }
    
    /**
     * Backup database
     */
    private function backupDatabase($targetPath) {
        if (!file_exists($this->dbFile)) {
            throw new Exception("ملف قاعدة البيانات غير موجود");
        }
        
        if (!copy($this->dbFile, $targetPath)) {
            throw new Exception("فشل في نسخ قاعدة البيانات");
        }
    }
    
    /**
     * Backup uploads directory
     */
    private function backupUploads($targetPath) {
        if (!is_dir('uploads')) {
            return; // No uploads to backup
        }
        
        $this->copyDirectory('uploads', $targetPath);
    }
    
    /**
     * Recursively copy directory
     */
    private function copyDirectory($source, $destination) {
        if (!is_dir($destination)) {
            mkdir($destination, 0755, true);
        }
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($source, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $item) {
            $target = $destination . DIRECTORY_SEPARATOR . $iterator->getSubPathName();
            
            if ($item->isDir()) {
                if (!is_dir($target)) {
                    mkdir($target, 0755, true);
                }
            } else {
                copy($item, $target);
            }
        }
    }
    
    /**
     * Create backup information file
     */
    private function createBackupInfo($backupPath, $timestamp) {
        $info = [
            'created_at' => $timestamp,
            'created_by' => 'System',
            'database_size' => file_exists($this->dbFile) ? filesize($this->dbFile) : 0,
            'uploads_count' => $this->countFiles('uploads'),
            'php_version' => PHP_VERSION,
            'system_version' => '1.0'
        ];
        
        file_put_contents($backupPath . '/backup_info.json', json_encode($info, JSON_PRETTY_PRINT));
    }
    
    /**
     * Count files in directory
     */
    private function countFiles($directory) {
        if (!is_dir($directory)) {
            return 0;
        }
        
        $count = 0;
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $count++;
            }
        }
        
        return $count;
    }
    
    /**
     * Clean old backups (keep only the latest ones)
     */
    private function cleanOldBackups() {
        $backups = glob($this->backupDir . 'backup_*', GLOB_ONLYDIR);
        
        if (count($backups) <= $this->maxBackups) {
            return;
        }
        
        // Sort by modification time (newest first)
        usort($backups, function($a, $b) {
            return filemtime($b) - filemtime($a);
        });
        
        // Remove old backups
        $backupsToRemove = array_slice($backups, $this->maxBackups);
        
        foreach ($backupsToRemove as $backup) {
            $this->removeDirectory($backup);
        }
    }
    
    /**
     * Remove directory recursively
     */
    public function removeDirectory($dir) {
        if (!is_dir($dir)) {
            return;
        }
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::CHILD_FIRST
        );
        
        foreach ($iterator as $file) {
            if ($file->isDir()) {
                rmdir($file->getRealPath());
            } else {
                unlink($file->getRealPath());
            }
        }
        
        rmdir($dir);
    }
    
    /**
     * List available backups
     */
    public function listBackups() {
        $backups = glob($this->backupDir . 'backup_*', GLOB_ONLYDIR);
        $backupList = [];
        
        foreach ($backups as $backup) {
            $backupName = basename($backup);
            $infoFile = $backup . '/backup_info.json';
            
            $info = [
                'name' => $backupName,
                'path' => $backup,
                'created_at' => date('Y-m-d H:i:s', filemtime($backup)),
                'size' => $this->getDirectorySize($backup)
            ];
            
            if (file_exists($infoFile)) {
                $backupInfo = json_decode(file_get_contents($infoFile), true);
                $info = array_merge($info, $backupInfo);
            }
            
            $backupList[] = $info;
        }
        
        // Sort by creation time (newest first)
        usort($backupList, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });
        
        return $backupList;
    }
    
    /**
     * Get directory size
     */
    private function getDirectorySize($directory) {
        $size = 0;
        
        if (!is_dir($directory)) {
            return $size;
        }
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $size += $file->getSize();
            }
        }
        
        return $size;
    }
    
    /**
     * Format file size
     */
    public static function formatSize($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    /**
     * Auto backup (called periodically)
     */
    public function autoBackup() {
        $lastBackup = $this->getLastBackupTime();
        $now = time();
        
        // Create backup if last backup is older than 24 hours
        if ($now - $lastBackup > 86400) { // 24 hours
            return $this->createFullBackup();
        }
        
        return false;
    }
    
    /**
     * Get last backup time
     */
    private function getLastBackupTime() {
        $backups = glob($this->backupDir . 'backup_*', GLOB_ONLYDIR);
        
        if (empty($backups)) {
            return 0;
        }
        
        $lastTime = 0;
        foreach ($backups as $backup) {
            $time = filemtime($backup);
            if ($time > $lastTime) {
                $lastTime = $time;
            }
        }
        
        return $lastTime;
    }
}

/**
 * Initialize auto backup
 */
function initAutoBackup() {
    if (defined('DB_FILE') && file_exists(DB_FILE)) {
        $backup = new BackupManager(DB_FILE);
        $backup->autoBackup();
    }
}
