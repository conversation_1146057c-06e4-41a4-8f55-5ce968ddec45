<?php
/**
 * Advanced Cache Manager for SAA Menu System
 * Handles file-based caching, image optimization, and performance improvements
 */

// Prevent direct access
if (!defined('DB_FILE')) {
    exit('Direct access not allowed');
}

class CacheManager {
    private $cacheDir;
    private $defaultTTL = 3600; // 1 hour default
    
    public function __construct($cacheDir = 'cache') {
        $this->cacheDir = __DIR__ . '/../' . $cacheDir;
        $this->ensureCacheDirectory();
    }
    
    /**
     * Ensure cache directory exists
     */
    private function ensureCacheDirectory() {
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
        
        // Create .htaccess to protect cache directory
        $htaccessPath = $this->cacheDir . '/.htaccess';
        if (!file_exists($htaccessPath)) {
            file_put_contents($htaccessPath, "Deny from all\n");
        }
    }
    
    /**
     * Generate cache key
     */
    private function getCacheKey($key) {
        return md5($key);
    }
    
    /**
     * Get cache file path
     */
    private function getCacheFilePath($key) {
        $cacheKey = $this->getCacheKey($key);
        return $this->cacheDir . '/' . $cacheKey . '.cache';
    }
    
    /**
     * Set cache data
     */
    public function set($key, $data, $ttl = null) {
        $ttl = $ttl ?? $this->defaultTTL;
        $cacheFile = $this->getCacheFilePath($key);
        
        $cacheData = [
            'data' => $data,
            'expires' => time() + $ttl,
            'created' => time()
        ];
        
        return file_put_contents($cacheFile, serialize($cacheData)) !== false;
    }
    
    /**
     * Get cache data
     */
    public function get($key) {
        $cacheFile = $this->getCacheFilePath($key);
        
        if (!file_exists($cacheFile)) {
            return null;
        }
        
        $cacheData = unserialize(file_get_contents($cacheFile));
        
        if (!$cacheData || $cacheData['expires'] < time()) {
            $this->delete($key);
            return null;
        }
        
        return $cacheData['data'];
    }
    
    /**
     * Check if cache exists and is valid
     */
    public function has($key) {
        return $this->get($key) !== null;
    }
    
    /**
     * Delete cache entry
     */
    public function delete($key) {
        $cacheFile = $this->getCacheFilePath($key);
        if (file_exists($cacheFile)) {
            return unlink($cacheFile);
        }
        return true;
    }
    
    /**
     * Clear all cache
     */
    public function clear() {
        $files = glob($this->cacheDir . '/*.cache');
        $cleared = 0;
        
        foreach ($files as $file) {
            if (unlink($file)) {
                $cleared++;
            }
        }
        
        return $cleared;
    }
    
    /**
     * Get cache statistics
     */
    public function getStats() {
        $files = glob($this->cacheDir . '/*.cache');
        $totalSize = 0;
        $validEntries = 0;
        $expiredEntries = 0;
        
        foreach ($files as $file) {
            $totalSize += filesize($file);
            $cacheData = unserialize(file_get_contents($file));
            
            if ($cacheData && $cacheData['expires'] >= time()) {
                $validEntries++;
            } else {
                $expiredEntries++;
            }
        }
        
        return [
            'total_files' => count($files),
            'valid_entries' => $validEntries,
            'expired_entries' => $expiredEntries,
            'total_size' => $totalSize,
            'total_size_formatted' => $this->formatBytes($totalSize)
        ];
    }
    
    /**
     * Clean expired cache entries
     */
    public function cleanExpired() {
        $files = glob($this->cacheDir . '/*.cache');
        $cleaned = 0;
        
        foreach ($files as $file) {
            $cacheData = unserialize(file_get_contents($file));
            
            if (!$cacheData || $cacheData['expires'] < time()) {
                if (unlink($file)) {
                    $cleaned++;
                }
            }
        }
        
        return $cleaned;
    }
    
    /**
     * Format bytes to human readable
     */
    private function formatBytes($bytes, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

/**
 * Image Optimizer Class
 */
class ImageOptimizer {
    private $quality = 85;
    private $maxWidth = 1200;
    private $maxHeight = 800;
    
    public function __construct($quality = 85, $maxWidth = 1200, $maxHeight = 800) {
        $this->quality = $quality;
        $this->maxWidth = $maxWidth;
        $this->maxHeight = $maxHeight;
    }
    
    /**
     * Optimize image
     */
    public function optimize($sourcePath, $destinationPath = null) {
        if (!file_exists($sourcePath)) {
            return false;
        }
        
        $destinationPath = $destinationPath ?? $sourcePath;
        $imageInfo = getimagesize($sourcePath);
        
        if (!$imageInfo) {
            return false;
        }
        
        $width = $imageInfo[0];
        $height = $imageInfo[1];
        $type = $imageInfo[2];
        
        // Calculate new dimensions
        $newDimensions = $this->calculateDimensions($width, $height);
        
        // Create image resource
        $sourceImage = $this->createImageResource($sourcePath, $type);
        if (!$sourceImage) {
            return false;
        }
        
        // Create new image
        $newImage = imagecreatetruecolor($newDimensions['width'], $newDimensions['height']);
        
        // Preserve transparency for PNG and GIF
        if ($type == IMAGETYPE_PNG || $type == IMAGETYPE_GIF) {
            imagealphablending($newImage, false);
            imagesavealpha($newImage, true);
            $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
            imagefilledrectangle($newImage, 0, 0, $newDimensions['width'], $newDimensions['height'], $transparent);
        }
        
        // Resize image
        imagecopyresampled(
            $newImage, $sourceImage,
            0, 0, 0, 0,
            $newDimensions['width'], $newDimensions['height'],
            $width, $height
        );
        
        // Save optimized image
        $result = $this->saveImage($newImage, $destinationPath, $type);
        
        // Clean up
        imagedestroy($sourceImage);
        imagedestroy($newImage);
        
        return $result;
    }
    
    /**
     * Calculate new dimensions while maintaining aspect ratio
     */
    private function calculateDimensions($width, $height) {
        if ($width <= $this->maxWidth && $height <= $this->maxHeight) {
            return ['width' => $width, 'height' => $height];
        }
        
        $ratio = min($this->maxWidth / $width, $this->maxHeight / $height);
        
        return [
            'width' => (int)($width * $ratio),
            'height' => (int)($height * $ratio)
        ];
    }
    
    /**
     * Create image resource from file
     */
    private function createImageResource($path, $type) {
        switch ($type) {
            case IMAGETYPE_JPEG:
                return imagecreatefromjpeg($path);
            case IMAGETYPE_PNG:
                return imagecreatefrompng($path);
            case IMAGETYPE_GIF:
                return imagecreatefromgif($path);
            case IMAGETYPE_WEBP:
                return imagecreatefromwebp($path);
            default:
                return false;
        }
    }
    
    /**
     * Save image to file
     */
    private function saveImage($image, $path, $type) {
        switch ($type) {
            case IMAGETYPE_JPEG:
                return imagejpeg($image, $path, $this->quality);
            case IMAGETYPE_PNG:
                return imagepng($image, $path, (int)(9 - ($this->quality / 10)));
            case IMAGETYPE_GIF:
                return imagegif($image, $path);
            case IMAGETYPE_WEBP:
                return imagewebp($image, $path, $this->quality);
            default:
                return false;
        }
    }
    
    /**
     * Get optimized image info
     */
    public function getOptimizedInfo($originalPath, $optimizedPath) {
        if (!file_exists($originalPath) || !file_exists($optimizedPath)) {
            return null;
        }
        
        $originalSize = filesize($originalPath);
        $optimizedSize = filesize($optimizedPath);
        $savings = $originalSize - $optimizedSize;
        $savingsPercent = ($savings / $originalSize) * 100;
        
        return [
            'original_size' => $originalSize,
            'optimized_size' => $optimizedSize,
            'savings' => $savings,
            'savings_percent' => round($savingsPercent, 2),
            'original_size_formatted' => $this->formatBytes($originalSize),
            'optimized_size_formatted' => $this->formatBytes($optimizedSize),
            'savings_formatted' => $this->formatBytes($savings)
        ];
    }
    
    /**
     * Format bytes to human readable
     */
    private function formatBytes($bytes, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

/**
 * Performance Monitor Class
 */
class PerformanceMonitor {
    private static $startTime;
    private static $queries = [];
    private static $memoryStart;
    
    public static function start() {
        self::$startTime = microtime(true);
        self::$memoryStart = memory_get_usage();
        self::$queries = [];
    }
    
    public static function logQuery($query, $executionTime) {
        self::$queries[] = [
            'query' => $query,
            'time' => $executionTime,
            'timestamp' => microtime(true)
        ];
    }
    
    public static function getStats() {
        $endTime = microtime(true);
        $memoryEnd = memory_get_usage();
        $memoryPeak = memory_get_peak_usage();
        
        return [
            'execution_time' => round(($endTime - self::$startTime) * 1000, 2), // ms
            'memory_usage' => $memoryEnd - self::$memoryStart,
            'memory_peak' => $memoryPeak,
            'queries_count' => count(self::$queries),
            'queries' => self::$queries,
            'memory_usage_formatted' => self::formatBytes($memoryEnd - self::$memoryStart),
            'memory_peak_formatted' => self::formatBytes($memoryPeak)
        ];
    }
    
    private static function formatBytes($bytes, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
