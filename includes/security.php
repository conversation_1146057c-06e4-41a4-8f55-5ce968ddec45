<?php
/**
 * Security Functions for SAA Menu System
 * Enhanced security measures and utilities
 */

// Prevent direct access
if (!defined('DB_FILE')) {
    exit('Direct access not allowed');
}

/**
 * Enhanced session security
 */
function initSecureSession() {
    // Session security settings
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
    ini_set('session.use_strict_mode', 1);
    ini_set('session.cookie_samesite', 'Strict');
    
    // Set session timeout (30 minutes)
    ini_set('session.gc_maxlifetime', 1800);
    
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // Check session timeout
    if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > 1800)) {
        session_unset();
        session_destroy();
        session_start();
    }
    $_SESSION['last_activity'] = time();
    
    // Regenerate session ID periodically
    if (!isset($_SESSION['created'])) {
        $_SESSION['created'] = time();
    } else if (time() - $_SESSION['created'] > 300) { // 5 minutes
        session_regenerate_id(true);
        $_SESSION['created'] = time();
    }
}

/**
 * Input sanitization functions
 */
function sanitizeInput($input, $type = 'string') {
    switch ($type) {
        case 'string':
            return filter_var(trim($input), FILTER_SANITIZE_STRING);
        case 'email':
            return filter_var(trim($input), FILTER_SANITIZE_EMAIL);
        case 'int':
            return filter_var($input, FILTER_VALIDATE_INT);
        case 'float':
            return filter_var($input, FILTER_VALIDATE_FLOAT);
        case 'url':
            return filter_var(trim($input), FILTER_SANITIZE_URL);
        case 'html':
            return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
        default:
            return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
}

/**
 * File upload security
 */
function validateImageUpload($file, $maxSize = 5242880, $maxWidth = 4000, $maxHeight = 4000) { // 5MB, 4000x4000 default
    $errors = [];

    // Check if file was uploaded
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        $errors[] = 'لم يتم رفع الملف بشكل صحيح';
        return $errors;
    }

    // Check file size
    if ($file['size'] > $maxSize) {
        $errors[] = 'حجم الملف كبير جداً. الحد الأقصى ' . ($maxSize / 1024 / 1024) . ' ميجابايت';
    }

    // Check if it's actually an image
    $imageInfo = getimagesize($file['tmp_name']);
    if ($imageInfo === false) {
        $errors[] = 'الملف ليس صورة صحيحة';
        return $errors;
    }

    // Check image dimensions
    if ($imageInfo[0] > $maxWidth || $imageInfo[1] > $maxHeight) {
        $errors[] = "أبعاد الصورة كبيرة جداً. الحد الأقصى {$maxWidth}x{$maxHeight} بكسل";
    }

    // Check minimum dimensions
    if ($imageInfo[0] < 100 || $imageInfo[1] < 100) {
        $errors[] = 'أبعاد الصورة صغيرة جداً. الحد الأدنى 100x100 بكسل';
    }

    // Check MIME type
    $allowedMimes = ['image/jpeg', 'image/png', 'image/gif'];
    if (!in_array($imageInfo['mime'], $allowedMimes)) {
        $errors[] = 'نوع الصورة غير مدعوم. المسموح: JPEG, PNG, GIF';
    }

    // Check file extension
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif'];
    if (!in_array($extension, $allowedExtensions)) {
        $errors[] = 'امتداد الملف غير مدعوم';
    }

    // Check for embedded scripts and malicious content
    $fileContent = file_get_contents($file['tmp_name']);
    $dangerousPatterns = [
        '<?php', '<?=', '<script', 'javascript:', 'vbscript:',
        'onload=', 'onerror=', 'onclick=', 'onmouseover=',
        'eval(', 'base64_decode', 'shell_exec', 'system(',
        'exec(', 'passthru', 'file_get_contents'
    ];

    foreach ($dangerousPatterns as $pattern) {
        if (stripos($fileContent, $pattern) !== false) {
            $errors[] = 'الملف يحتوي على محتوى غير آمن';
            break;
        }
    }

    // Additional security: Check for PHP in EXIF data
    if (function_exists('exif_read_data') && in_array($imageInfo['mime'], ['image/jpeg', 'image/tiff'])) {
        $exifData = @exif_read_data($file['tmp_name']);
        if ($exifData) {
            $exifString = serialize($exifData);
            foreach ($dangerousPatterns as $pattern) {
                if (stripos($exifString, $pattern) !== false) {
                    $errors[] = 'الملف يحتوي على بيانات غير آمنة';
                    break;
                }
            }
        }
    }

    return $errors;
}

/**
 * CSRF Protection
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Rate Limiting for Login Attempts
 */
function checkRateLimit($ip, $maxAttempts = 5, $timeWindow = 900) { // 15 minutes
    $rateLimitFile = __DIR__ . '/../data/rate_limits.json';

    // Create data directory if it doesn't exist
    if (!is_dir(dirname($rateLimitFile))) {
        mkdir(dirname($rateLimitFile), 0755, true);
    }

    $rateLimits = [];
    if (file_exists($rateLimitFile)) {
        $rateLimits = json_decode(file_get_contents($rateLimitFile), true) ?: [];
    }

    $currentTime = time();

    // Clean old entries
    foreach ($rateLimits as $storedIp => $data) {
        if ($currentTime - $data['first_attempt'] > $timeWindow) {
            unset($rateLimits[$storedIp]);
        }
    }

    // Check current IP
    if (!isset($rateLimits[$ip])) {
        $rateLimits[$ip] = [
            'attempts' => 1,
            'first_attempt' => $currentTime
        ];
    } else {
        $rateLimits[$ip]['attempts']++;
    }

    // Save updated rate limits
    file_put_contents($rateLimitFile, json_encode($rateLimits));

    return $rateLimits[$ip]['attempts'] <= $maxAttempts;
}

function resetRateLimit($ip) {
    $rateLimitFile = __DIR__ . '/../data/rate_limits.json';

    if (file_exists($rateLimitFile)) {
        $rateLimits = json_decode(file_get_contents($rateLimitFile), true) ?: [];
        unset($rateLimits[$ip]);
        file_put_contents($rateLimitFile, json_encode($rateLimits));
    }
}

/**
 * Security Logging
 */
function logSecurityEvent($event, $details = '') {
    $logFile = __DIR__ . '/../data/security.log';

    // Create data directory if it doesn't exist
    if (!is_dir(dirname($logFile))) {
        mkdir(dirname($logFile), 0755, true);
    }

    $timestamp = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';

    $logEntry = "[{$timestamp}] IP: {$ip} | Event: {$event} | Details: {$details} | User-Agent: {$userAgent}\n";

    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

/**
 * Installation and Database Validation
 */
function isInstallationComplete() {
    return file_exists(__DIR__ . '/../config.php') && !file_exists(__DIR__ . '/../install.php');
}

function validateDatabaseConnection($dbFile) {
    try {
        $pdo = new PDO('sqlite:' . $dbFile);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Check if required tables exist
        $tables = ['settings', 'categories', 'pages'];
        foreach ($tables as $table) {
            $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='{$table}'");
            if (!$stmt->fetch()) {
                return false;
            }
        }

        return true;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Create uploads directory with proper security
 */
function createUploadsDirectory($uploadDir = 'uploads') {
    if (!is_dir($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            throw new Exception('فشل في إنشاء مجلد الرفع');
        }
    }

    // Create .htaccess file for security
    $htaccessFile = $uploadDir . '/.htaccess';
    if (!file_exists($htaccessFile)) {
        $htaccessContent = "# Prevent script execution\n";
        $htaccessContent .= "<Files \"*.php\">\n";
        $htaccessContent .= "    Order allow,deny\n";
        $htaccessContent .= "    Deny from all\n";
        $htaccessContent .= "</Files>\n";
        $htaccessContent .= "<Files \"*.phtml\">\n";
        $htaccessContent .= "    Order allow,deny\n";
        $htaccessContent .= "    Deny from all\n";
        $htaccessContent .= "</Files>\n";
        $htaccessContent .= "<Files \"*.php3\">\n";
        $htaccessContent .= "    Order allow,deny\n";
        $htaccessContent .= "    Deny from all\n";
        $htaccessContent .= "</Files>\n";
        $htaccessContent .= "<Files \"*.php4\">\n";
        $htaccessContent .= "    Order allow,deny\n";
        $htaccessContent .= "    Deny from all\n";
        $htaccessContent .= "</Files>\n";
        $htaccessContent .= "<Files \"*.php5\">\n";
        $htaccessContent .= "    Order allow,deny\n";
        $htaccessContent .= "    Deny from all\n";
        $htaccessContent .= "</Files>\n";

        file_put_contents($htaccessFile, $htaccessContent);
    }

    return true;
}

/**
 * Generate secure filename
 */
function generateSecureFilename($originalName, $prefix = '') {
    $extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
    $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif'];

    if (!in_array($extension, $allowedExtensions)) {
        throw new Exception('امتداد الملف غير مدعوم');
    }

    $timestamp = time();
    $randomString = bin2hex(random_bytes(8));
    $prefix = $prefix ? $prefix . '_' : '';

    return $prefix . $timestamp . '_' . $randomString . '.' . $extension;
}

/**
 * Secure file upload handler
 */
function handleSecureUpload($file, $uploadDir = 'uploads', $prefix = '') {
    // Create upload directory
    createUploadsDirectory($uploadDir);

    // Validate file
    $errors = validateImageUpload($file);
    if (!empty($errors)) {
        throw new Exception(implode(', ', $errors));
    }

    // Generate secure filename
    $filename = generateSecureFilename($file['name'], $prefix);
    $targetPath = $uploadDir . '/' . $filename;

    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $targetPath)) {
        throw new Exception('فشل في رفع الملف');
    }

    // Set proper permissions
    chmod($targetPath, 0644);

    return $filename;
}



/**
 * Check for suspicious activity
 */
function detectSuspiciousActivity() {
    $suspiciousPatterns = [
        'union select',
        'drop table',
        'insert into',
        'delete from',
        'update set',
        '<script',
        'javascript:',
        'vbscript:',
        'onload=',
        'onerror=',
        '../',
        '..\\',
        'etc/passwd',
        'boot.ini'
    ];
    
    $requestData = array_merge($_GET, $_POST, $_COOKIE);
    
    foreach ($requestData as $key => $value) {
        if (is_string($value)) {
            foreach ($suspiciousPatterns as $pattern) {
                if (stripos($value, $pattern) !== false) {
                    logSecurityEvent('Suspicious Activity Detected', "Pattern: {$pattern} in {$key}");
                    return true;
                }
            }
        }
    }
    
    return false;
}

/**
 * Rate limiting helper
 */
function isRateLimited($action, $limit = 5, $timeWindow = 900) { // 15 minutes default
    if (!isset($_SESSION['rate_limits'])) {
        $_SESSION['rate_limits'] = [];
    }
    
    if (!isset($_SESSION['rate_limits'][$action])) {
        $_SESSION['rate_limits'][$action] = [];
    }
    
    $now = time();
    
    // Clean old entries
    $_SESSION['rate_limits'][$action] = array_filter(
        $_SESSION['rate_limits'][$action],
        function($timestamp) use ($now, $timeWindow) {
            return ($now - $timestamp) < $timeWindow;
        }
    );
    
    // Check if limit exceeded
    if (count($_SESSION['rate_limits'][$action]) >= $limit) {
        return true;
    }
    
    // Record this attempt
    $_SESSION['rate_limits'][$action][] = $now;
    
    return false;
}
