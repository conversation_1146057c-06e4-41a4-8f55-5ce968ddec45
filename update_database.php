<?php
/**
 * Database Update Script for Responsive Images Feature
 * This script adds the responsive_images table to existing installations
 */

// Configuration
define('DB_FILE', 'menu_system.db');

// Check if database exists
if (!file_exists(DB_FILE)) {
    die('❌ ملف قاعدة البيانات غير موجود. يرجى تشغيل install.php أولاً.');
}

try {
    // Connect to database
    $pdo = new PDO('sqlite:' . DB_FILE);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if responsive_images table already exists
    $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='responsive_images'");
    $tableExists = $stmt->fetch();
    
    if ($tableExists) {
        echo "✅ جدول الصور المتجاوبة موجود بالفعل. لا حاجة للتحديث.\n";
        exit;
    }
    
    // Create responsive_images table
    $sql = "
    CREATE TABLE `responsive_images` (
      `id` INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
      `page_id` INTEGER NOT NULL,
      `size_lang` TEXT NOT NULL,
      `filename` TEXT NOT NULL,
      `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (`page_id`) REFERENCES `pages` (`id`) ON DELETE CASCADE,
      UNIQUE(`page_id`, `size_lang`)
    );
    ";
    
    $pdo->exec($sql);
    
    // Create responsive directory if it doesn't exist
    if (!is_dir('uploads/responsive')) {
        mkdir('uploads/responsive', 0755, true);
        echo "✅ تم إنشاء مجلد uploads/responsive\n";
    }
    
    echo "✅ تم تحديث قاعدة البيانات بنجاح!\n";
    echo "✅ تم إضافة جدول الصور المتجاوبة.\n";
    echo "🎉 يمكنك الآن استخدام ميزة الصور المتجاوبة من لوحة التحكم.\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في تحديث قاعدة البيانات: " . $e->getMessage() . "\n";
    exit(1);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث قاعدة البيانات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        
        .success {
            color: #28a745;
            font-size: 18px;
            margin: 20px 0;
            padding: 15px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 30px;
            background: #8B4513;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 10px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #6d3410;
        }
        
        .features {
            text-align: right;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .features h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .features ul {
            list-style: none;
            padding: 0;
        }
        
        .features li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .features li:last-child {
            border-bottom: none;
        }
        
        .emoji {
            font-size: 24px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="emoji">🎉</div>
        <h1>تم تحديث قاعدة البيانات بنجاح!</h1>
        
        <div class="success">
            ✅ تم إضافة ميزة الصور المتجاوبة بنجاح
        </div>
        
        <div class="features">
            <h3>الميزات الجديدة المتاحة:</h3>
            <ul>
                <li>📱 رفع صور مخصصة للهواتف المحمولة</li>
                <li>📱 رفع صور مخصصة للأجهزة اللوحية</li>
                <li>💻 رفع صور مخصصة لأجهزة سطح المكتب</li>
                <li>🌍 دعم اللغتين العربية والإنجليزية لكل حجم</li>
                <li>⚡ تحميل تلقائي للصورة المناسبة حسب حجم الشاشة</li>
                <li>🔄 تحديث تلقائي عند تغيير اتجاه الشاشة</li>
            </ul>
        </div>
        
        <div>
            <a href="admin.php?page=responsive-images" class="btn">🖼️ إدارة الصور المتجاوبة</a>
            <a href="admin.php" class="btn">🏠 لوحة التحكم</a>
            <a href="index.php" class="btn">👁️ عرض القائمة</a>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: #e3f2fd; border-radius: 8px; font-size: 14px;">
            <strong>💡 نصيحة:</strong> لأفضل النتائج، استخدم الأحجام المقترحة:
            <br>📱 هاتف صغير: 375x812 بكسل (iPhone SE وما شابه)
            <br>📱 هاتف كبير: 430x932 بكسل (iPhone Pro Max وما شابه)
            <br>📱 آيباد طولي: 768x1024 بكسل (الأجهزة اللوحية)
        </div>
    </div>
</body>
</html>
