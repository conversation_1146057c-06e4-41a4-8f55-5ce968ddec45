<?php
/**
 * Database Update Script for New Color Settings
 * This script adds the new color settings to existing installations
 */

// Configuration
define('DB_FILE', 'menu_system.db');

// Check if database exists
if (!file_exists(DB_FILE)) {
    die('❌ ملف قاعدة البيانات غير موجود. يرجى تشغيل install.php أولاً.');
}

try {
    // Connect to database
    $pdo = new PDO('sqlite:' . DB_FILE);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if new color settings already exist
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM settings WHERE setting_key IN ('text_color', 'language_btn_bg', 'language_btn_text', 'active_text_color')");
    $stmt->execute();
    $existingCount = $stmt->fetchColumn();

    if ($existingCount >= 4) {
        echo "✅ إعدادات الألوان الجديدة موجودة بالفعل. لا حاجة للتحديث.\n";
        exit;
    }

    // Add new color settings
    $new_settings = [
        ['text_color', '#333333'],
        ['language_btn_bg', 'rgba(255, 255, 255, 0.85)'],
        ['language_btn_text', '#333333'],
        ['active_text_color', '#FFFFFF']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)");
    foreach ($new_settings as $setting) {
        $stmt->execute($setting);
    }
    
    echo "✅ تم تحديث قاعدة البيانات بنجاح!\n";
    echo "✅ تم إضافة إعدادات الألوان الجديدة.\n";
    echo "🎉 يمكنك الآن التحكم في ألوان النصوص وزر اللغة من لوحة التحكم.\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في تحديث قاعدة البيانات: " . $e->getMessage() . "\n";
    exit(1);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث إعدادات الألوان</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        
        .success {
            color: #28a745;
            font-size: 18px;
            margin: 20px 0;
            padding: 15px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 30px;
            background: #8B4513;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 10px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #6d3410;
        }
        
        .features {
            text-align: right;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .features h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .features ul {
            list-style: none;
            padding: 0;
        }
        
        .features li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .features li:last-child {
            border-bottom: none;
        }
        
        .emoji {
            font-size: 24px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="emoji">🎨</div>
        <h1>تم تحديث إعدادات الألوان بنجاح!</h1>
        
        <div class="success">
            ✅ تم إضافة التحكم في الألوان الجديدة بنجاح
        </div>
        
        <div class="features">
            <h3>الألوان الجديدة المتاحة:</h3>
            <ul>
                <li>🎨 لون نص أزرار الأقسام</li>
                <li>✨ لون نص الزر النشط</li>
                <li>🌐 لون نص زر اللغة</li>
                <li>🔍 لون خلفية زر اللغة مع التحكم في الشفافية</li>
                <li>⚡ تحديث فوري للألوان في الواجهة</li>
                <li>🎯 تخصيص كامل لجميع عناصر القائمة</li>
            </ul>
        </div>
        
        <div>
            <a href="admin.php?page=settings" class="btn">🎨 إعدادات الألوان</a>
            <a href="admin.php" class="btn">🏠 لوحة التحكم</a>
            <a href="index.php" class="btn">👁️ عرض القائمة</a>
        </div>
    </div>
</body>
</html>
