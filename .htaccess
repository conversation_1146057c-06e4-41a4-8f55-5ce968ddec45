# Security Headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Head<PERSON> always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' fonts.googleapis.com; font-src 'self' fonts.gstatic.com; img-src 'self' data:; connect-src 'self';"
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
</IfModule>

# Prevent access to sensitive files
<Files "*.db">
    Order allow,deny
    Deny from all
</Files>

<Files "config.php">
    Order allow,deny
    Deny from all
</Files>

<Files ".htaccess">
    Order allow,deny
    Deny from all
</Files>

# Block access to log files
<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

# Block access to backup files
<Files "*.bak">
    Order allow,deny
    Deny from all
</Files>

<Files "*.backup">
    Order allow,deny
    Deny from all
</Files>

# Block access to temporary files
<Files "*.tmp">
    Order allow,deny
    <PERSON>y from all
</Files>

# Block access to version control files
<Files ".git*">
    Order allow,deny
    Deny from all
</Files>

<Files ".svn*">
    Order allow,deny
    Deny from all
</Files>

# Prevent directory browsing
Options -Indexes

# Protect data directory
<Directory "data">
    Order allow,deny
    Deny from all
</Directory>

# Protect includes directory
<Directory "includes">
    <Files "*.php">
        Order allow,deny
        Deny from all
    </Files>
</Directory>

# Protect admin directory from direct access
<Directory "admin">
    <Files "*.php">
        Order allow,deny
        Deny from all
    </Files>
</Directory>

# Protect uploads directory from script execution
<Directory "uploads">
    <Files "*.php">
        Order allow,deny
        Deny from all
    </Files>
    <Files "*.phtml">
        Order allow,deny
        Deny from all
    </Files>
    <Files "*.php3">
        Order allow,deny
        Deny from all
    </Files>
    <Files "*.php4">
        Order allow,deny
        Deny from all
    </Files>
    <Files "*.php5">
        Order allow,deny
        Deny from all
    </Files>
    <Files "*.pl">
        Order allow,deny
        Deny from all
    </Files>
    <Files "*.py">
        Order allow,deny
        Deny from all
    </Files>
    <Files "*.jsp">
        Order allow,deny
        Deny from all
    </Files>
    <Files "*.asp">
        Order allow,deny
        Deny from all
    </Files>
    <Files "*.sh">
        Order allow,deny
        Deny from all
    </Files>
</Directory>

# Cache static files
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
</IfModule>

# URL Rewriting for clean URLs (if needed in future)
RewriteEngine On

# Prevent access to install.php after installation
<Files "install.php">
    <RequireAll>
        Require all denied
    </RequireAll>
</Files>
