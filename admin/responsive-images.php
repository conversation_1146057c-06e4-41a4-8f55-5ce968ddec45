<?php
// Ensure the script is not accessed directly
if (!defined('DB_FILE')) {
    exit('Direct script access is not allowed.');
}

// تعريف أحجام الصور المبسطة - 3 أحجام فقط
$image_sizes = [
    'small_phone' => [
        'name_ar' => 'هاتف صغير',
        'name_en' => 'Small Phone',
        'width' => 375,
        'height' => 812,
        'description' => 'للهواتف الصغيرة مثل iPhone SE (375×812)'
    ],
    'large_phone' => [
        'name_ar' => 'هاتف كبير',
        'name_en' => 'Large Phone',
        'width' => 430,
        'height' => 932,
        'description' => 'للهواتف الكبيرة مثل iPhone Pro Max (430×932)'
    ],
    'ipad' => [
        'name_ar' => 'آيباد طولي',
        'name_en' => 'iPad Portrait',
        'width' => 768,
        'height' => 1024,
        'description' => 'للأجهزة اللوحية في الوضع الطولي (768×1024)'
    ]
];

$action = $_GET['action'] ?? 'list';
$page_id = $_GET['page_id'] ?? 0;
$message = '';
$error = '';

// File upload directory
define('UPLOAD_DIR', __DIR__ . '/../uploads/');
define('RESPONSIVE_DIR', UPLOAD_DIR . 'responsive/');

// Create responsive directory if it doesn't exist
if (!is_dir(RESPONSIVE_DIR)) {
    mkdir(RESPONSIVE_DIR, 0755, true);
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $error = 'رمز الأمان غير صحيح. يرجى المحاولة مرة أخرى.';
    } else {
        $page_id = (int)($_POST['page_id'] ?? 0);
        
        // Handle responsive image uploads
        try {
            $responsive_images = [];
            
            // Handle different sizes
            $sizes = ['mobile', 'tablet', 'desktop'];
            $languages = ['ar', 'en'];
            
            foreach ($sizes as $size) {
                foreach ($languages as $lang) {
                    $field_name = "image_{$size}_{$lang}";
                    if (isset($_FILES[$field_name]) && $_FILES[$field_name]['error'] === UPLOAD_ERR_OK) {
                        $filename = handle_responsive_upload($_FILES[$field_name], $size, $lang);
                        $responsive_images["{$size}_{$lang}"] = $filename;
                    }
                }
            }
            
            // Save to database
            if (!empty($responsive_images)) {
                foreach ($responsive_images as $key => $filename) {
                    $stmt = $pdo->prepare("INSERT OR REPLACE INTO responsive_images (page_id, size_lang, filename) VALUES (?, ?, ?)");
                    $stmt->execute([$page_id, $key, $filename]);
                }
                $message = 'تم رفع الصور المتجاوبة بنجاح!';
            }
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// Function to handle responsive image uploads
function handle_responsive_upload($file, $size, $lang) {
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    $max_size = 5 * 1024 * 1024; // 5MB
    
    if (!in_array($file['type'], $allowed_types)) {
        throw new Exception('نوع الملف غير مدعوم. يرجى رفع صورة بصيغة JPG, PNG, GIF أو WebP.');
    }
    
    if ($file['size'] > $max_size) {
        throw new Exception('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت.');
    }
    
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid() . "_{$size}_{$lang}." . $extension;
    $filepath = RESPONSIVE_DIR . $filename;
    
    if (!move_uploaded_file($file['tmp_name'], $filepath)) {
        throw new Exception('فشل في رفع الملف.');
    }
    
    return $filename;
}

// Get pages for dropdown
$pages = $pdo->query("SELECT p.id, p.image_ar, c.name_ar as category_name FROM pages p JOIN categories c ON p.category_id = c.id ORDER BY c.sort_order ASC, p.sort_order ASC")->fetchAll();

// Get existing responsive images
$responsive_images = [];
if ($page_id) {
    $stmt = $pdo->prepare("SELECT * FROM responsive_images WHERE page_id = ?");
    $stmt->execute([$page_id]);
    $responsive_images = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
}
?>

<div class="admin-header">
    <h2>🖼️ إدارة الصور المتجاوبة</h2>
    <p>رفع أحجام مختلفة من الصور لتحسين العرض على الأجهزة المختلفة</p>
</div>

<?php if ($message): ?>
    <div class="alert alert-success"><?= htmlspecialchars($message) ?></div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-error"><?= htmlspecialchars($error) ?></div>
<?php endif; ?>

<div class="card">
    <h3>📱 رفع صور متجاوبة</h3>
    
    <form method="post" enctype="multipart/form-data" class="form-grid">
        <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
        
        <div class="form-group">
            <label for="page_id">اختر الصفحة:</label>
            <select name="page_id" id="page_id" required onchange="loadExistingImages(this.value)">
                <option value="">-- اختر صفحة --</option>
                <?php foreach ($pages as $page): ?>
                    <option value="<?= $page['id'] ?>" <?= $page_id == $page['id'] ? 'selected' : '' ?>>
                        <?= htmlspecialchars($page['category_name']) ?> - صفحة <?= $page['id'] ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <div class="responsive-upload-grid">
            <div class="size-section">
                <h4>📱 هاتف محمول (320px-480px)</h4>
                <div class="upload-row">
                    <label>عربي:</label>
                    <input type="file" name="image_mobile_ar" accept="image/*">
                    <?php if (isset($responsive_images['mobile_ar'])): ?>
                        <span class="existing-file">✅ موجود</span>
                    <?php endif; ?>
                </div>
                <div class="upload-row">
                    <label>إنجليزي:</label>
                    <input type="file" name="image_mobile_en" accept="image/*">
                    <?php if (isset($responsive_images['mobile_en'])): ?>
                        <span class="existing-file">✅ موجود</span>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="size-section">
                <h4>📱 تابلت (768px-1024px)</h4>
                <div class="upload-row">
                    <label>عربي:</label>
                    <input type="file" name="image_tablet_ar" accept="image/*">
                    <?php if (isset($responsive_images['tablet_ar'])): ?>
                        <span class="existing-file">✅ موجود</span>
                    <?php endif; ?>
                </div>
                <div class="upload-row">
                    <label>إنجليزي:</label>
                    <input type="file" name="image_tablet_en" accept="image/*">
                    <?php if (isset($responsive_images['tablet_en'])): ?>
                        <span class="existing-file">✅ موجود</span>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="size-section">
                <h4>💻 سطح المكتب (1024px+)</h4>
                <div class="upload-row">
                    <label>عربي:</label>
                    <input type="file" name="image_desktop_ar" accept="image/*">
                    <?php if (isset($responsive_images['desktop_ar'])): ?>
                        <span class="existing-file">✅ موجود</span>
                    <?php endif; ?>
                </div>
                <div class="upload-row">
                    <label>إنجليزي:</label>
                    <input type="file" name="image_desktop_en" accept="image/*">
                    <?php if (isset($responsive_images['desktop_en'])): ?>
                        <span class="existing-file">✅ موجود</span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="form-actions">
            <button type="submit" class="btn btn-primary">💾 حفظ الصور</button>
        </div>
    </form>
</div>

<div class="card">
    <h3>📋 نصائح لتحسين الصور</h3>
    <div class="tips-grid">
        <div class="tip">
            <h4>📱 الهواتف المحمولة</h4>
            <p>الحجم المثالي: 480x800 بكسل<br>
            حجم الملف: أقل من 200 كيلوبايت<br>
            التركيز على الوضوح والبساطة</p>
        </div>
        <div class="tip">
            <h4>📱 الأجهزة اللوحية</h4>
            <p>الحجم المثالي: 768x1024 بكسل<br>
            حجم الملف: أقل من 500 كيلوبايت<br>
            توازن بين الجودة والحجم</p>
        </div>
        <div class="tip">
            <h4>💻 سطح المكتب</h4>
            <p>الحجم المثالي: 1200x800 بكسل<br>
            حجم الملف: أقل من 1 ميجابايت<br>
            جودة عالية للعرض الكبير</p>
        </div>
    </div>
</div>

<style>
.responsive-upload-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.size-section {
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    background: #f9f9f9;
}

.size-section h4 {
    margin: 0 0 15px 0;
    color: #333;
    border-bottom: 1px solid #ddd;
    padding-bottom: 8px;
}

.upload-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.upload-row label {
    min-width: 60px;
    font-weight: bold;
}

.existing-file {
    color: #28a745;
    font-size: 12px;
    font-weight: bold;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.tip {
    background: #f0f8ff;
    border: 1px solid #b3d9ff;
    border-radius: 6px;
    padding: 15px;
}

.tip h4 {
    margin: 0 0 10px 0;
    color: #0066cc;
}

.tip p {
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
}
</style>

<script>
function loadExistingImages(pageId) {
    if (pageId) {
        window.location.href = '?page=responsive-images&page_id=' + pageId;
    }
}
</script>
