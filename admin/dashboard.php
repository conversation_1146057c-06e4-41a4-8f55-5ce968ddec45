<?php
// Ensure the script is not accessed directly
if (!defined('DB_FILE')) {
    exit('Direct script access is not allowed.');
}

// Fetch stats
$total_categories = $pdo->query("SELECT COUNT(*) FROM categories")->fetchColumn();
$total_pages = $pdo->query("SELECT COUNT(*) FROM pages")->fetchColumn();

?>
<style>
.stats-container {
    display: flex;
    gap: 20px;
    justify-content: space-around;
    text-align: center;
}
.stat-card {
    background-color: #f4f4f4;
    padding: 30px;
    border-radius: 8px;
    flex-grow: 1;
    border: 1px solid #ddd;
}
.stat-card h3 {
    margin: 0 0 10px 0;
    font-size: 1.2rem;
    color: #555;
}
.stat-card p {
    margin: 0;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
}
</style>

<h2>نظرة عامة</h2>
<div class="stats-container">
    <div class="stat-card">
        <h3>إجمالي الأقسام</h3>
        <p><?php echo $total_categories; ?></p>
    </div>
    <div class="stat-card">
        <h3>إجمالي الصفحات</h3>
        <p><?php echo $total_pages; ?></p>
    </div>
</div>

<div style="margin-top: 40px;">
    <h3>روابط سريعة</h3>
    <ul>
        <li><a href="admin.php?page=categories">إدارة الأقسام</a></li>
        <li><a href="admin.php?page=pages">إدارة الصفحات</a></li>
        <li><a href="admin.php?page=settings">تعديل الإعدادات</a></li>
    </ul>
</div>