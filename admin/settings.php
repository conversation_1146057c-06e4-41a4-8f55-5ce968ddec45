<?php
// Ensure the script is not accessed directly
if (!defined('DB_FILE')) {
    exit('Direct script access is not allowed.');
}

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Update general settings
    $settings_to_update = [
        'site_title' => $_POST['site_title'] ?? '',
        'primary_color' => $_POST['primary_color'] ?? '#8B4513',
        'secondary_color' => $_POST['secondary_color'] ?? '#FFFFFF',
        'button_bg_color' => $_POST['button_bg_color'] ?? '#FFFFFF',
        'text_color' => $_POST['text_color'] ?? '#333333',
        'language_btn_bg' => $_POST['language_btn_bg'] ?? 'rgba(255, 255, 255, 0.85)',
        'language_btn_text' => $_POST['language_btn_text'] ?? '#333333',
        'active_text_color' => $_POST['active_text_color'] ?? '#FFFFFF',
        'preloader_bg_color' => $_POST['preloader_bg_color'] ?? '#FFFFFF',
    ];

    // Update active languages
    $active_langs = isset($_POST['active_langs']) && is_array($_POST['active_langs']) ? implode(',', $_POST['active_langs']) : '';
    $settings_to_update['lang_active'] = $active_langs;

    // Handle logo upload
    if (isset($_FILES['preloader_logo']) && $_FILES['preloader_logo']['error'] === UPLOAD_ERR_OK) {
        $uploadDir = '../uploads/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        $fileInfo = pathinfo($_FILES['preloader_logo']['name']);
        $allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'svg'];

        if (in_array(strtolower($fileInfo['extension']), $allowedTypes)) {
            $fileName = 'preloader_logo.' . $fileInfo['extension'];
            $uploadPath = $uploadDir . $fileName;

            if (move_uploaded_file($_FILES['preloader_logo']['tmp_name'], $uploadPath)) {
                $settings_to_update['preloader_logo'] = 'uploads/' . $fileName;
            }
        }
    }

    $stmt = $pdo->prepare("UPDATE settings SET setting_value = ? WHERE setting_key = ?");
    foreach ($settings_to_update as $key => $value) {
        $stmt->execute([$value, $key]);
    }

    // Update admin password if provided
    if (!empty($_POST['admin_pass'])) {
        if ($_POST['admin_pass'] === $_POST['admin_pass_confirm']) {
            $hashed_password = password_hash($_POST['admin_pass'], PASSWORD_DEFAULT);
            $stmt->execute([$hashed_password, 'admin_pass']);
        } else {
            $error = 'كلمتا المرور غير متطابقتين.';
        }
    }
    
    if (!$error) {
        $message = 'تم تحديث الإعدادات بنجاح!';
    }
    
    // Refresh settings variable
    $stmt = $pdo->query("SELECT * FROM settings");
    $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
}

$current_active_langs = explode(',', $settings['lang_active']);
?>

<?php if ($message): ?><div class="alert alert-success" style="margin-bottom: 20px;"><?php echo $message; ?></div><?php endif; ?>
<?php if ($error): ?><div class="alert alert-danger" style="margin-bottom: 20px;"><?php echo $error; ?></div><?php endif; ?>

<form method="POST" action="admin.php?page=settings">
    <h3>الإعدادات العامة</h3>
    <div class="form-group">
        <label for="site_title">عنوان الموقع</label>
        <input type="text" id="site_title" name="site_title" value="<?php echo htmlspecialchars($settings['site_title']); ?>" required>
    </div>
    <div class="form-group">
        <label for="primary_color">اللون الأساسي (للعناصر النشطة)</label>
        <input type="color" id="primary_color" name="primary_color" value="<?php echo htmlspecialchars($settings['primary_color']); ?>">
    </div>
    <div class="form-group">
        <label for="secondary_color">لون الشريط السفلي</label>
        <input type="color" id="secondary_color" name="secondary_color" value="<?php echo htmlspecialchars($settings['secondary_color']); ?>">
    </div>
    <div class="form-group">
        <label for="button_bg_color">لون خلفية أزرار الأقسام</label>
        <input type="color" id="button_bg_color" name="button_bg_color" value="<?php echo htmlspecialchars($settings['button_bg_color'] ?? '#FFFFFF'); ?>">
    </div>
    <div class="form-group">
        <label for="text_color">لون نص أزرار الأقسام</label>
        <input type="color" id="text_color" name="text_color" value="<?php echo htmlspecialchars($settings['text_color'] ?? '#333333'); ?>">
    </div>
    <div class="form-group">
        <label for="language_btn_text">لون نص زر اللغة</label>
        <input type="color" id="language_btn_text" name="language_btn_text" value="<?php echo htmlspecialchars($settings['language_btn_text'] ?? '#333333'); ?>">
    </div>
    <div class="form-group">
        <label for="language_btn_bg">لون خلفية زر اللغة (شفافية)</label>
        <div style="display: flex; align-items: center; gap: 10px;">
            <input type="color" id="language_btn_bg_color" value="#FFFFFF" onchange="updateLanguageBtnBg()">
            <input type="range" id="language_btn_bg_opacity" min="0" max="100" value="85" onchange="updateLanguageBtnBg()">
            <span id="opacity_value">85%</span>
            <input type="hidden" id="language_btn_bg" name="language_btn_bg" value="<?php echo htmlspecialchars($settings['language_btn_bg'] ?? 'rgba(255, 255, 255, 0.85)'); ?>">
        </div>
        <small style="color: #666;">اختر اللون والشفافية لخلفية زر اللغة</small>
    </div>
    <div class="form-group">
        <label for="active_text_color">لون نص الزر النشط</label>
        <input type="color" id="active_text_color" name="active_text_color" value="<?php echo htmlspecialchars($settings['active_text_color'] ?? '#FFFFFF'); ?>">
    </div>

    <hr style="margin: 30px 0;">

    <h3>إعدادات اللغة</h3>
    <div class="form-group">
        <label>اللغات المفعلة</label>
        <div>
            <label><input type="checkbox" name="active_langs[]" value="ar" <?php echo in_array('ar', $current_active_langs) ? 'checked' : ''; ?>> العربية</label>
        </div>
        <div>
            <label><input type="checkbox" name="active_langs[]" value="en" <?php echo in_array('en', $current_active_langs) ? 'checked' : ''; ?>> الإنجليزية</label>
        </div>
    </div>

    <hr style="margin: 30px 0;">

    <h3>تغيير كلمة المرور</h3>
    <div class="form-group">
        <label for="admin_pass">كلمة المرور الجديدة</label>
        <input type="password" id="admin_pass" name="admin_pass" placeholder="اتركها فارغة لعدم التغيير">
    </div>
    <div class="form-group">
        <label for="admin_pass_confirm">تأكيد كلمة المرور الجديدة</label>
        <input type="password" id="admin_pass_confirm" name="admin_pass_confirm">
    </div>

    <button type="submit" class="btn btn-primary">حفظ الإعدادات</button>
</form>

<script>
function updateLanguageBtnBg() {
    const colorInput = document.getElementById('language_btn_bg_color');
    const opacityInput = document.getElementById('language_btn_bg_opacity');
    const opacityValue = document.getElementById('opacity_value');
    const hiddenInput = document.getElementById('language_btn_bg');

    const color = colorInput.value;
    const opacity = parseFloat(opacityInput.value) / 100;

    // Convert hex to RGB
    const r = parseInt(color.substring(1, 3), 16);
    const g = parseInt(color.substring(3, 5), 16);
    const b = parseInt(color.substring(5, 7), 16);

    const rgbaValue = `rgba(${r}, ${g}, ${b}, ${opacity.toFixed(2)})`;
    hiddenInput.value = rgbaValue;
    opacityValue.textContent = opacityInput.value + '%';

    // Debug log
    console.log('Updated language button background:', rgbaValue);
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Parse current value to set color and opacity
    const currentValue = document.getElementById('language_btn_bg').value;
    console.log('Current language_btn_bg value:', currentValue);

    const rgbaMatch = currentValue.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/);

    if (rgbaMatch) {
        const r = parseInt(rgbaMatch[1]);
        const g = parseInt(rgbaMatch[2]);
        const b = parseInt(rgbaMatch[3]);
        const a = parseFloat(rgbaMatch[4]);

        // Convert RGB to hex
        const hex = '#' + [r, g, b].map(x => {
            const hex = x.toString(16);
            return hex.length === 1 ? '0' + hex : hex;
        }).join('').toUpperCase();

        document.getElementById('language_btn_bg_color').value = hex;
        document.getElementById('language_btn_bg_opacity').value = Math.round(a * 100);
        document.getElementById('opacity_value').textContent = Math.round(a * 100) + '%';

        console.log('Parsed values - Hex:', hex, 'Opacity:', Math.round(a * 100));
    } else {
        // Fallback for non-rgba values
        console.log('Could not parse rgba, using defaults');
        document.getElementById('language_btn_bg_color').value = '#FFFFFF';
        document.getElementById('language_btn_bg_opacity').value = 85;
        document.getElementById('opacity_value').textContent = '85%';
        updateLanguageBtnBg();
    }
});
</script>