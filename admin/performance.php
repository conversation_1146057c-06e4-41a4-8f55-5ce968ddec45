<?php
// Ensure the script is not accessed directly
if (!defined('DB_FILE')) {
    exit('Direct script access is not allowed.');
}

require_once __DIR__ . '/../includes/database_optimization.php';
require_once __DIR__ . '/../includes/backup.php';

$action = $_GET['action'] ?? 'dashboard';
$message = '';
$error = '';

$optimizer = new DatabaseOptimizer($pdo);

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Check CSRF token
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $error = 'رمز الأمان غير صحيح. يرجى المحاولة مرة أخرى.';
    } else {
        if (isset($_POST['optimize_database'])) {
            try {
                if ($optimizer->optimizeDatabase()) {
                    $message = "تم تحسين قاعدة البيانات بنجاح";
                } else {
                    $error = "فشل في تحسين قاعدة البيانات";
                }
            } catch (Exception $e) {
                $error = $e->getMessage();
            }
        } elseif (isset($_POST['create_indexes'])) {
            try {
                if ($optimizer->createIndexes()) {
                    $message = "تم إنشاء الفهارس بنجاح";
                } else {
                    $error = "فشل في إنشاء الفهارس";
                }
            } catch (Exception $e) {
                $error = $e->getMessage();
            }
        } elseif (isset($_POST['clear_cache'])) {
            QueryCache::clear();
            $message = "تم مسح الذاكرة المؤقتة بنجاح";
        }
    }
}

// Get database analysis
$analysis = $optimizer->analyzeDatabase();
$stats = $optimizer->getDatabaseStats();
$integrity = $optimizer->checkIntegrity();

?>

<?php if ($message): ?>
    <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
<?php endif; ?>

<div class="performance-dashboard">
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <h3>إحصائيات قاعدة البيانات</h3>
                <?php if ($stats): ?>
                <ul>
                    <li><strong>عدد الصفحات:</strong> <?php echo number_format($stats['page_count']); ?></li>
                    <li><strong>حجم الصفحة:</strong> <?php echo number_format($stats['page_size']); ?> بايت</li>
                    <li><strong>الصفحات الفارغة:</strong> <?php echo number_format($stats['free_pages']); ?></li>
                    <li><strong>كفاءة قاعدة البيانات:</strong> <?php echo number_format($stats['efficiency'], 2); ?>%</li>
                    <li><strong>حجم قاعدة البيانات:</strong> <?php echo file_exists(DB_FILE) ? BackupManager::formatSize(filesize(DB_FILE)) : 'غير متوفر'; ?></li>
                </ul>
                <?php else: ?>
                <p>فشل في تحميل إحصائيات قاعدة البيانات</p>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <h3>حالة النظام</h3>
                <ul>
                    <li><strong>سلامة قاعدة البيانات:</strong> 
                        <span class="<?php echo $integrity ? 'text-success' : 'text-danger'; ?>">
                            <?php echo $integrity ? 'سليمة' : 'تالفة'; ?>
                        </span>
                    </li>
                    <li><strong>إصدار PHP:</strong> <?php echo PHP_VERSION; ?></li>
                    <li><strong>إصدار SQLite:</strong> <?php echo SQLite3::version()['versionString']; ?></li>
                    <li><strong>الذاكرة المستخدمة:</strong> <?php echo BackupManager::formatSize(memory_get_usage(true)); ?></li>
                    <li><strong>الحد الأقصى للذاكرة:</strong> <?php echo ini_get('memory_limit'); ?></li>
                </ul>
            </div>
        </div>
    </div>
    
    <?php if ($analysis): ?>
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <h3>أحجام الجداول</h3>
                <table>
                    <thead>
                        <tr>
                            <th>الجدول</th>
                            <th>عدد السجلات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($analysis['table_sizes'] as $table => $count): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($table); ?></td>
                            <td><?php echo number_format($count); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <h3>الفهارس المتوفرة</h3>
                <?php foreach ($analysis['indexes'] as $table => $indexes): ?>
                <h4>جدول <?php echo htmlspecialchars($table); ?></h4>
                <?php if (!empty($indexes)): ?>
                <table>
                    <thead>
                        <tr>
                            <th>اسم الفهرس</th>
                            <th>فريد</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($indexes as $index): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($index['name']); ?></td>
                            <td><?php echo $index['unique'] ? 'نعم' : 'لا'; ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                <?php else: ?>
                <p>لا توجد فهارس مخصصة</p>
                <?php endif; ?>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <h3>أدوات التحسين</h3>
                <div class="optimization-tools">
                    <form method="POST" style="display: inline-block; margin-left: 10px;">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <button type="submit" name="optimize_database" class="btn btn-primary">تحسين قاعدة البيانات</button>
                    </form>
                    
                    <form method="POST" style="display: inline-block; margin-left: 10px;">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <button type="submit" name="create_indexes" class="btn btn-secondary">إنشاء الفهارس</button>
                    </form>
                    
                    <form method="POST" style="display: inline-block;">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <button type="submit" name="clear_cache" class="btn btn-warning">مسح الذاكرة المؤقتة</button>
                    </form>
                </div>
                
                <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
                    <h4>نصائح للأداء</h4>
                    <ul>
                        <li>قم بتحسين قاعدة البيانات بانتظام لاستعادة المساحة المحررة</li>
                        <li>تأكد من وجود الفهارس على الأعمدة المستخدمة في البحث</li>
                        <li>امسح الذاكرة المؤقتة بعد إجراء تغييرات كبيرة</li>
                        <li>راقب حجم قاعدة البيانات وقم بأرشفة البيانات القديمة عند الحاجة</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.performance-dashboard .row {
    display: flex;
    margin-bottom: 20px;
    gap: 20px;
}

.performance-dashboard .col-md-6 {
    flex: 1;
}

.performance-dashboard .col-md-12 {
    flex: 1;
}

.text-success {
    color: #28a745;
    font-weight: bold;
}

.text-danger {
    color: #dc3545;
    font-weight: bold;
}

.optimization-tools {
    margin-bottom: 20px;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
    border: none;
    border-radius: 3px;
    padding: 10px 20px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
}

.btn-warning:hover {
    background-color: #e0a800;
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}
</style>
