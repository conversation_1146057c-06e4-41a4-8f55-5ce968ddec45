<?php
// معاينة مبسطة للهاتف المحمول
?>

<style>
.preview-container {
    display: flex;
    gap: 20px;
    margin: 20px 0;
}

.device-preview {
    border: 2px solid #ddd;
    border-radius: 20px;
    padding: 20px;
    background: #f8f9fa;
    text-align: center;
    flex: 1;
}

.device-frame {
    width: 300px;
    height: 600px;
    border: 8px solid #333;
    border-radius: 25px;
    margin: 0 auto;
    background: white;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.device-screen {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 17px;
    background: white;
}

.preview-controls {
    margin: 15px 0;
}

.preview-btn {
    background: var(--primary, #8B4513);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    margin: 0 5px;
    font-size: 14px;
}

.preview-btn:hover {
    opacity: 0.9;
}

.preview-btn.active {
    background: #654321;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #ddd;
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary, #8B4513);
    display: block;
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
    margin-top: 5px;
}

.optimization-tips {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.optimization-tips h4 {
    color: #0066cc;
    margin-bottom: 15px;
}

.optimization-tips ul {
    list-style-type: none;
    padding: 0;
}

.optimization-tips li {
    padding: 8px 0;
    border-bottom: 1px solid #d0e7ff;
}

.optimization-tips li:last-child {
    border-bottom: none;
}

.optimization-tips li::before {
    content: "💡 ";
    margin-right: 8px;
}

.performance-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.performance-good { background: #28a745; }
.performance-warning { background: #ffc107; }
.performance-poor { background: #dc3545; }
</style>

<div class="preview-container">
    <div class="device-preview">
        <h3>📱 معاينة الهاتف المحمول</h3>
        <div class="device-frame" id="deviceFrame">
            <iframe src="../index.php" class="device-screen" id="mobilePreview" frameborder="0"></iframe>
        </div>
        <div class="preview-controls">
            <button class="preview-btn active" onclick="setDeviceSize('small-phone')" id="small-phone-btn">📱 هاتف صغير</button>
            <button class="preview-btn" onclick="setDeviceSize('large-phone')" id="large-phone-btn">📱 هاتف كبير</button>
            <button class="preview-btn" onclick="setDeviceSize('ipad')" id="ipad-btn">📱 آيباد طولي</button>
            <button class="preview-btn" onclick="refreshPreview()">🔄 تحديث</button>
        </div>
    </div>
    
    <div style="flex: 1;">
        <h3>📊 إحصائيات الأداء</h3>
        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-number"><?php echo count($categories); ?></span>
                <div class="stat-label">الفئات النشطة</div>
            </div>
            <div class="stat-card">
                <span class="stat-number"><?php echo count($pages); ?></span>
                <div class="stat-label">الصفحات النشطة</div>
            </div>
            <div class="stat-card">
                <span class="stat-number"><?php 
                    $totalSize = 0;
                    foreach($pages as $page) {
                        $imagePath = '../uploads/' . $page['image_ar'];
                        if (file_exists($imagePath)) {
                            $totalSize += filesize($imagePath);
                        }
                    }
                    echo round($totalSize / 1024 / 1024, 1);
                ?>MB</span>
                <div class="stat-label">حجم الصور الإجمالي</div>
            </div>
            <div class="stat-card">
                <span class="performance-indicator <?php 
                    $avgSize = count($pages) > 0 ? $totalSize / count($pages) / 1024 : 0;
                    if ($avgSize < 500) echo 'performance-good';
                    elseif ($avgSize < 1000) echo 'performance-warning';
                    else echo 'performance-poor';
                ?>"></span>
                <span class="stat-number"><?php echo round($avgSize); ?>KB</span>
                <div class="stat-label">متوسط حجم الصورة</div>
            </div>
        </div>
        
        <div class="optimization-tips">
            <h4>💡 نصائح التحسين</h4>
            <ul>
                <li>استخدم صور بصيغة WebP لتقليل الحجم بنسبة 25-35%</li>
                <li>احرص على أن يكون حجم الصورة أقل من 500KB للأداء الأمثل</li>
                <li>استخدم أبعاد مناسبة للشاشات المختلفة</li>
                <li>فعّل ضغط الصور التلقائي في إعدادات النظام</li>
                <li>راجع الصور غير المستخدمة وقم بحذفها</li>
                <li>استخدم أسماء ملفات وصفية لتحسين SEO</li>
            </ul>
        </div>
        
        <div style="background: white; padding: 20px; border-radius: 8px; border: 1px solid #ddd;">
            <h4>🔧 أدوات سريعة</h4>
            <button class="preview-btn" onclick="refreshPreview()">🔄 تحديث المعاينة</button>
            <button class="preview-btn" onclick="testMobileSpeed()">⚡ اختبار السرعة</button>
            <button class="preview-btn" onclick="optimizeImages()">🖼️ تحسين الصور</button>
            <button class="preview-btn" onclick="clearCache()">🗑️ مسح التخزين المؤقت</button>
        </div>
    </div>
</div>

<script>
function setDeviceSize(type) {
    const frame = document.getElementById('deviceFrame');
    const buttons = document.querySelectorAll('.preview-btn');

    // Remove active class from all buttons
    buttons.forEach(btn => btn.classList.remove('active'));

    // Add active class to clicked button
    document.getElementById(type + '-btn').classList.add('active');

    switch(type) {
        case 'small-phone':
            frame.style.width = '375px';
            frame.style.height = '667px';
            frame.style.transform = 'scale(0.8)';
            break;
        case 'large-phone':
            frame.style.width = '414px';
            frame.style.height = '736px';
            frame.style.transform = 'scale(0.7)';
            break;
        case 'ipad':
            frame.style.width = '768px';
            frame.style.height = '1024px';
            frame.style.transform = 'scale(0.5)';
            break;
    }

    // Refresh iframe after resize
    setTimeout(() => {
        const iframe = document.getElementById('mobilePreview');
        iframe.src = iframe.src;
    }, 100);
}

function refreshPreview() {
    const iframe = document.getElementById('mobilePreview');
    iframe.src = iframe.src;
    showToast('تم تحديث المعاينة');
}

function testMobileSpeed() {
    showToast('جاري اختبار السرعة...', 'info');
    // Simulate speed test
    setTimeout(() => {
        showToast('سرعة التحميل: 2.3 ثانية - جيد!', 'success');
    }, 2000);
}

function optimizeImages() {
    showToast('جاري تحسين الصور...', 'info');
    // This would trigger actual image optimization
    setTimeout(() => {
        showToast('تم تحسين الصور بنجاح!', 'success');
    }, 3000);
}

function clearCache() {
    showToast('تم مسح التخزين المؤقت', 'success');
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : type === 'info' ? '#17a2b8' : '#ffc107'};
        color: white;
        padding: 12px 20px;
        border-radius: 6px;
        z-index: 10000;
        font-size: 14px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        transition: all 0.3s ease;
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.style.opacity = '0';
        toast.style.transform = 'translateX(20px)';
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 3000);
}
</script>
