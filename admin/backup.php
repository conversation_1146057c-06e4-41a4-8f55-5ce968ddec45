<?php
// Ensure the script is not accessed directly
if (!defined('DB_FILE')) {
    exit('Direct script access is not allowed.');
}

require_once __DIR__ . '/../includes/backup.php';

$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

$backup = new BackupManager(DB_FILE);

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Check CSRF token
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $error = 'رمز الأمان غير صحيح. يرجى المحاولة مرة أخرى.';
    } else {
        if (isset($_POST['create_backup'])) {
            try {
                $backupName = $backup->createFullBackup();
                $message = "تم إنشاء النسخة الاحتياطية بنجاح: {$backupName}";
            } catch (Exception $e) {
                $error = $e->getMessage();
            }
        }
    }
}

// Handle delete backup
if ($action === 'delete' && isset($_GET['backup'])) {
    $backupName = $_GET['backup'];
    $backupPath = 'backups/' . $backupName;
    
    if (is_dir($backupPath) && strpos($backupName, 'backup_') === 0) {
        try {
            $backup->removeDirectory($backupPath);
            $message = "تم حذف النسخة الاحتياطية: {$backupName}";
        } catch (Exception $e) {
            $error = "فشل في حذف النسخة الاحتياطية: " . $e->getMessage();
        }
    } else {
        $error = "النسخة الاحتياطية غير موجودة أو غير صالحة.";
    }
    $action = 'list';
}

?>

<?php if ($message): ?>
    <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
<?php endif; ?>

<div style="margin-bottom: 20px;">
    <form method="POST" style="display: inline;">
        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
        <button type="submit" name="create_backup" class="btn btn-primary">إنشاء نسخة احتياطية جديدة</button>
    </form>
</div>

<h3>النسخ الاحتياطية المتوفرة</h3>

<?php
$backups = $backup->listBackups();

if (empty($backups)): ?>
    <p>لا توجد نسخ احتياطية متوفرة.</p>
<?php else: ?>
    <table>
        <thead>
            <tr>
                <th>اسم النسخة</th>
                <th>تاريخ الإنشاء</th>
                <th>الحجم</th>
                <th>عدد الملفات</th>
                <th>إجراءات</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($backups as $backupInfo): ?>
            <tr>
                <td><?php echo htmlspecialchars($backupInfo['name']); ?></td>
                <td><?php echo htmlspecialchars($backupInfo['created_at']); ?></td>
                <td><?php echo BackupManager::formatSize($backupInfo['size']); ?></td>
                <td><?php echo isset($backupInfo['uploads_count']) ? $backupInfo['uploads_count'] : 'غير محدد'; ?></td>
                <td>
                    <a href="admin.php?page=backup&action=delete&backup=<?php echo urlencode($backupInfo['name']); ?>" 
                       onclick="return confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟');" 
                       class="btn btn-danger btn-sm">حذف</a>
                </td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
<?php endif; ?>

<div style="margin-top: 30px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
    <h4>معلومات النظام</h4>
    <ul>
        <li><strong>حجم قاعدة البيانات:</strong> <?php echo file_exists(DB_FILE) ? BackupManager::formatSize(filesize(DB_FILE)) : 'غير متوفر'; ?></li>
        <li><strong>عدد ملفات الرفع:</strong> <?php echo is_dir('uploads') ? count(glob('uploads/*')) : '0'; ?></li>
        <li><strong>مساحة النسخ الاحتياطية:</strong> <?php echo is_dir('backups') ? BackupManager::formatSize(array_sum(array_map('filesize', glob('backups/*/*', GLOB_NOSORT)))) : '0 B'; ?></li>
        <li><strong>آخر نسخة احتياطية:</strong> <?php echo !empty($backups) ? $backups[0]['created_at'] : 'لا توجد'; ?></li>
    </ul>
</div>

<div style="margin-top: 20px; padding: 15px; background-color: #fff3cd; border-radius: 5px; border: 1px solid #ffeaa7;">
    <h4>ملاحظات مهمة</h4>
    <ul>
        <li>يتم إنشاء نسخة احتياطية تلقائية كل 24 ساعة</li>
        <li>يتم الاحتفاظ بآخر 10 نسخ احتياطية فقط</li>
        <li>النسخ الاحتياطية تشمل قاعدة البيانات وملفات الرفع وملف الإعدادات</li>
        <li>يُنصح بتحميل النسخ الاحتياطية إلى مكان آمن خارج الخادم</li>
    </ul>
</div>

<style>
.btn-sm {
    padding: 5px 10px;
    font-size: 12px;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
}

.btn-danger:hover {
    background-color: #c82333;
    color: white;
    text-decoration: none;
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}
</style>
